#!/usr/bin/env python3
"""
Epic GUI Preview - Shows the new amazing interface
"""

import tkinter as tk
from tkinter import ttk, font
import time

class EpicGUIPreview:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("💀 TANTRA DEATH BOT - INSANE CYBERPUNK GUI 💀")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)

        # INSANE CYBERPUNK COLOR SCHEME 🔥
        self.colors = {
            'bg_primary': '#000000',       # Pure black matrix
            'bg_secondary': '#0d1117',     # Dark matrix
            'bg_accent': '#161b22',        # Accent matrix
            'bg_glow': '#1a1f26',          # Glow background
            'neon_cyan': '#00ffff',        # Cyberpunk cyan
            'neon_pink': '#ff00ff',        # Hot pink neon
            'neon_green': '#00ff00',       # Matrix green
            'neon_orange': '#ff4500',      # Blazing orange
            'neon_purple': '#8a2be2',      # Electric purple
            'neon_yellow': '#ffff00',      # Lightning yellow
            'neon_red': '#ff0040',         # Danger red
            'neon_blue': '#0080ff',        # Electric blue
            'text_primary': '#ffffff',     # Pure white
            'text_glow': '#00ffff',        # Glowing cyan text
            'text_matrix': '#00ff00',      # Matrix green text
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
        
        # INSANE CYBERPUNK FONTS 💀
        self.fonts = {
            'mega_title': font.Font(family="Impact", size=36, weight="bold"),
            'title': font.Font(family="Impact", size=24, weight="bold"),
            'header': font.Font(family="Arial Black", size=14, weight="bold"),
            'cyber_header': font.Font(family="Courier New", size=12, weight="bold"),
            'normal': font.Font(family="Arial", size=11, weight="bold"),
            'matrix': font.Font(family="Consolas", size=8, weight="bold")
        }
        
        self.setup_epic_style()
        self.create_preview()
        
    def setup_epic_style(self):
        """Setup epic styling"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Epic Notebook styling
        style.configure('Epic.TNotebook', 
                       background=self.colors['bg_secondary'],
                       borderwidth=0,
                       tabmargins=[2, 5, 2, 0])
        
        style.configure('Epic.TNotebook.Tab',
                       background=self.colors['bg_accent'],
                       foreground=self.colors['text_secondary'],
                       padding=[20, 10],
                       borderwidth=1,
                       focuscolor='none',
                       font=self.fonts['header'])
        
        style.map('Epic.TNotebook.Tab',
                 background=[('selected', self.colors['accent_primary']),
                           ('active', self.colors['accent_secondary'])],
                 foreground=[('selected', self.colors['text_primary']),
                           ('active', self.colors['text_primary'])])
        
        # Epic LabelFrame styling
        style.configure('Epic.TLabelframe',
                       background=self.colors['bg_secondary'],
                       borderwidth=2,
                       relief='groove')
        
        style.configure('Epic.TLabelframe.Label',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_primary'],
                       font=self.fonts['header'])
        
    def create_preview(self):
        """Create the epic preview"""
        main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Epic header
        header_frame = tk.Frame(main_container, bg=self.colors['bg_primary'], height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # Matrix background
        matrix_bg = tk.Label(header_frame,
                            text="01001000 01000001 01000011 01001011 01000101 01010010",
                            bg=self.colors['bg_primary'],
                            fg=self.colors['bg_accent'],
                            font=self.fonts['matrix'])
        matrix_bg.pack(pady=(5, 0))

        # INSANE TITLE
        title_label = tk.Label(header_frame,
                              text="💀 TANTRA DEATH BOT 💀",
                              bg=self.colors['bg_primary'],
                              fg=self.colors['neon_cyan'],
                              font=self.fonts['mega_title'])
        title_label.pack(pady=(5, 0))

        # Subtitle
        subtitle = tk.Label(header_frame,
                           text="🔥 ULTIMATE DESTRUCTION SYSTEM 🔥",
                           bg=self.colors['bg_primary'],
                           fg=self.colors['neon_orange'],
                           font=self.fonts['header'])
        subtitle.pack()

        version_label = tk.Label(header_frame,
                                text=">>> VERSION 2.0.0 | NEURAL NETWORK ACTIVATED <<<",
                                bg=self.colors['bg_primary'],
                                fg=self.colors['neon_green'],
                                font=self.fonts['cyber_header'])
        version_label.pack(pady=(5, 0))

        # Multiple status indicators
        status_frame = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        status_frame.pack(pady=(10, 0))

        self.status1 = tk.Label(status_frame, text="◉ SYSTEM", bg=self.colors['bg_primary'],
                               fg=self.colors['neon_green'], font=self.fonts['header'])
        self.status1.pack(side='left', padx=10)

        self.status2 = tk.Label(status_frame, text="◉ NEURAL", bg=self.colors['bg_primary'],
                               fg=self.colors['neon_cyan'], font=self.fonts['header'])
        self.status2.pack(side='left', padx=10)

        self.status3 = tk.Label(status_frame, text="◉ MATRIX", bg=self.colors['bg_primary'],
                               fg=self.colors['neon_pink'], font=self.fonts['header'])
        self.status3.pack(side='left', padx=10)
        
        # Epic notebook
        notebook = ttk.Notebook(main_container, style='Epic.TNotebook')
        notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # INSANE CYBERPUNK TABS
        tabs = [
            ("💀 DEATH CONTROL 💀", "ULTIMATE DESTRUCTION INTERFACE"),
            ("⚔️ COMBAT MATRIX ⚔️", "NEURAL COMBAT ALGORITHMS"),
            ("✨ POWER SURGE ✨", "ENERGY AMPLIFICATION SYSTEM"),
            ("💊 LIFE SUPPORT 💊", "BIOLOGICAL MAINTENANCE PROTOCOLS"),
            ("🔐 NEURAL LINK 🔐", "MATRIX CONNECTION INTERFACE"),
            ("⚙️ CYBER CORE ⚙️", "SYSTEM CONFIGURATION MATRIX"),
            ("🚀 QUANTUM LEAP 🚀", "ADVANCED REALITY MANIPULATION"),
            ("🛠️ HACK TOOLS 🛠️", "DIGITAL WARFARE ARSENAL")
        ]
        
        for tab_name, description in tabs:
            frame = ttk.Frame(notebook, style='Epic.TFrame')
            notebook.add(frame, text=tab_name)
            
            # Sample content
            content_frame = ttk.LabelFrame(frame, text=f"🔥 {description.upper()}", style='Epic.TLabelframe')
            content_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            sample_label = tk.Label(content_frame,
                                   text=f"This is the {description}\nwith epic dark theme styling!",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_primary'],
                                   font=self.fonts['normal'],
                                   justify='center')
            sample_label.pack(expand=True)
        
        # Epic control buttons
        control_frame = tk.Frame(main_container, bg=self.colors['bg_primary'], height=80)
        control_frame.pack(side=tk.BOTTOM, fill='x', padx=5, pady=10)
        control_frame.pack_propagate(False)
        
        buttons = [
            ("💀 ACTIVATE DEATH MODE 💀", self.colors['neon_green']),
            ("⚡ SUSPEND MATRIX ⚡", self.colors['neon_yellow']),
            ("🔥 TERMINATE SYSTEM 🔥", self.colors['neon_red']),
            ("💾 SAVE NEURAL DATA 💾", self.colors['neon_blue']),
            ("📁 LOAD CONSCIOUSNESS 📁", self.colors['neon_purple'])
        ]
        
        for text, color in buttons:
            btn = tk.Button(control_frame,
                           text=text,
                           bg=color,
                           fg=self.colors['bg_primary'],
                           font=self.fonts['header'],
                           borderwidth=5,
                           relief='raised',
                           padx=25,
                           pady=15,
                           cursor='hand2')
            btn.pack(side='left', padx=10)
        
        # INSANE status bar
        status_bar = tk.Label(main_container,
                             text="💀 CYBERPUNK DEATH INTERFACE LOADED - NEURAL NETWORK ONLINE 💀",
                             bg=self.colors['bg_secondary'],
                             fg=self.colors['neon_cyan'],
                             font=self.fonts['cyber_header'],
                             relief=tk.GROOVE,
                             borderwidth=4,
                             anchor=tk.W,
                             padx=15,
                             pady=8)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        
        # Start animation
        self.animate_status()
        
    def animate_status(self):
        """INSANE status animation with multiple indicators"""
        try:
            import random

            # Animate multiple status indicators
            colors1 = [self.colors['neon_green'], self.colors['neon_cyan'], self.colors['neon_blue']]
            colors2 = [self.colors['neon_cyan'], self.colors['neon_purple'], self.colors['neon_pink']]
            colors3 = [self.colors['neon_pink'], self.colors['neon_orange'], self.colors['neon_red']]

            self.status1.config(fg=random.choice(colors1))
            self.status2.config(fg=random.choice(colors2))
            self.status3.config(fg=random.choice(colors3))

            # Occasionally change text
            if random.randint(1, 20) == 1:
                texts1 = ["◉ SYSTEM", "◉ ONLINE", "◉ ACTIVE", "◉ READY"]
                texts2 = ["◉ NEURAL", "◉ BRAIN", "◉ AI", "◉ CORE"]
                texts3 = ["◉ MATRIX", "◉ HACK", "◉ CODE", "◉ CYBER"]

                self.status1.config(text=random.choice(texts1))
                self.status2.config(text=random.choice(texts2))
                self.status3.config(text=random.choice(texts3))

            self.root.after(100, self.animate_status)
        except:
            pass
        
    def run(self):
        """Run the preview"""
        self.root.mainloop()

if __name__ == "__main__":
    preview = EpicGUIPreview()
    preview.run()

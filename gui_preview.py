#!/usr/bin/env python3
"""
Epic GUI Preview - Shows the new amazing interface
"""

import tkinter as tk
from tkinter import ttk, font
import time

class EpicGUIPreview:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔥 TANTRA BOT SYSTEM - EPIC GUI PREVIEW 🔥")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # Epic color scheme
        self.colors = {
            'bg_primary': '#0a0a0a',      # Deep black
            'bg_secondary': '#1a1a1a',    # Dark gray
            'bg_accent': '#2d2d30',       # Medium gray
            'accent_primary': '#ff6b35',   # Epic orange
            'accent_secondary': '#f7931e', # Golden orange
            'text_primary': '#ffffff',     # White
            'text_secondary': '#cccccc',   # Light gray
            'success': '#00ff88',          # Neon green
            'warning': '#ffaa00',          # Orange warning
            'danger': '#ff3366',           # Red danger
            'info': '#00aaff',             # Blue info
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
        
        # Custom fonts
        self.fonts = {
            'title': font.Font(family="Arial Black", size=16, weight="bold"),
            'header': font.Font(family="Arial", size=12, weight="bold"),
            'normal': font.Font(family="Arial", size=10),
        }
        
        self.setup_epic_style()
        self.create_preview()
        
    def setup_epic_style(self):
        """Setup epic styling"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Epic Notebook styling
        style.configure('Epic.TNotebook', 
                       background=self.colors['bg_secondary'],
                       borderwidth=0,
                       tabmargins=[2, 5, 2, 0])
        
        style.configure('Epic.TNotebook.Tab',
                       background=self.colors['bg_accent'],
                       foreground=self.colors['text_secondary'],
                       padding=[20, 10],
                       borderwidth=1,
                       focuscolor='none',
                       font=self.fonts['header'])
        
        style.map('Epic.TNotebook.Tab',
                 background=[('selected', self.colors['accent_primary']),
                           ('active', self.colors['accent_secondary'])],
                 foreground=[('selected', self.colors['text_primary']),
                           ('active', self.colors['text_primary'])])
        
        # Epic LabelFrame styling
        style.configure('Epic.TLabelframe',
                       background=self.colors['bg_secondary'],
                       borderwidth=2,
                       relief='groove')
        
        style.configure('Epic.TLabelframe.Label',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_primary'],
                       font=self.fonts['header'])
        
    def create_preview(self):
        """Create the epic preview"""
        main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Epic header
        header_frame = tk.Frame(main_container, bg=self.colors['bg_primary'], height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame,
                              text="⚡ TANTRA BOT SYSTEM ⚡",
                              bg=self.colors['bg_primary'],
                              fg=self.colors['accent_primary'],
                              font=self.fonts['title'])
        title_label.pack(pady=(10, 0))
        
        version_label = tk.Label(header_frame,
                                text="Version 2.0.0 | Elite Gaming Automation",
                                bg=self.colors['bg_primary'],
                                fg=self.colors['text_secondary'],
                                font=self.fonts['normal'])
        version_label.pack()
        
        # Animated status
        self.status_indicator = tk.Label(header_frame,
                                        text="● EPIC MODE ACTIVATED",
                                        bg=self.colors['bg_primary'],
                                        fg=self.colors['success'],
                                        font=self.fonts['header'])
        self.status_indicator.pack(pady=(5, 0))
        
        # Epic notebook
        notebook = ttk.Notebook(main_container, style='Epic.TNotebook')
        notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Sample tabs
        tabs = [
            ("🎮 MAIN", "Main control panel with epic styling"),
            ("⚔️ SKILLS", "Combat skills configuration"),
            ("✨ BUFFS", "Buff management system"),
            ("💊 HEALING", "HP/TP healing options"),
            ("🔐 LOGIN", "Auto-login configuration"),
            ("⚙️ SETTINGS", "Advanced settings"),
            ("🚀 ADVANCED", "Pro features"),
            ("🛠️ TOOLS", "Utility tools")
        ]
        
        for tab_name, description in tabs:
            frame = ttk.Frame(notebook, style='Epic.TFrame')
            notebook.add(frame, text=tab_name)
            
            # Sample content
            content_frame = ttk.LabelFrame(frame, text=f"🔥 {description.upper()}", style='Epic.TLabelframe')
            content_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            sample_label = tk.Label(content_frame,
                                   text=f"This is the {description}\nwith epic dark theme styling!",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['text_primary'],
                                   font=self.fonts['normal'],
                                   justify='center')
            sample_label.pack(expand=True)
        
        # Epic control buttons
        control_frame = tk.Frame(main_container, bg=self.colors['bg_primary'], height=80)
        control_frame.pack(side=tk.BOTTOM, fill='x', padx=5, pady=10)
        control_frame.pack_propagate(False)
        
        buttons = [
            ("🚀 START BOT", self.colors['success']),
            ("⏸️ PAUSE", self.colors['warning']),
            ("⏹️ STOP", self.colors['danger']),
            ("💾 SAVE", self.colors['info']),
            ("📁 LOAD", self.colors['accent_secondary'])
        ]
        
        for text, color in buttons:
            btn = tk.Button(control_frame,
                           text=text,
                           bg=color,
                           fg=self.colors['text_primary'],
                           font=self.fonts['header'],
                           borderwidth=0,
                           padx=20,
                           pady=10,
                           cursor='hand2')
            btn.pack(side='left', padx=5)
        
        # Epic status bar
        status_bar = tk.Label(main_container,
                             text="🔥 EPIC GUI LOADED - Ready for gaming automation!",
                             bg=self.colors['bg_accent'],
                             fg=self.colors['accent_primary'],
                             font=self.fonts['normal'],
                             relief=tk.RAISED,
                             borderwidth=2,
                             anchor=tk.W,
                             padx=10,
                             pady=5)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))
        
        # Start animation
        self.animate_status()
        
    def animate_status(self):
        """Animate the status indicator"""
        try:
            current_color = self.status_indicator.cget('fg')
            if current_color == self.colors['success']:
                self.status_indicator.config(fg=self.colors['accent_primary'])
            else:
                self.status_indicator.config(fg=self.colors['success'])
            self.root.after(500, self.animate_status)
        except:
            pass
        
    def run(self):
        """Run the preview"""
        self.root.mainloop()

if __name__ == "__main__":
    preview = EpicGUIPreview()
    preview.run()

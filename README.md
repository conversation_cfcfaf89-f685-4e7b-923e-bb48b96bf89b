# Tantra Bot System - Python Version

A comprehensive automation bot for the Tantra MMORPG, rebuilt from the original AutoIt version by booster101.

## Features

### Core Automation
- **Auto Combat**: Automated skill rotation with customizable delays
- **Auto Targeting**: Intelligent monster targeting with configurable monster lists
- **Auto Looting**: Automatic item collection
- **Auto Healing**: Smart HP/TP management with potions and healing skills
- **Auto Buffing**: Timed buff casting with duration tracking

### Advanced Features
- **Auto Login**: Complete automated login sequence with server/character selection
- **Auto Repair**: Equipment durability monitoring and repair
- **Auto Silfrijan**: Automatic resurrection using Silfrijan
- **Anti-Stuck**: Movement patterns to prevent getting stuck
- **Dialog Handling**: Auto-accept/deny party invites, trade requests, and duels
- **PM Auto-Reply**: Automatic responses to private messages

### Quality of Life
- **Hotkey Support**: Global hotkeys for all major functions
- **Configuration Management**: Save/load bot configurations
- **Multi-Tab Interface**: Organized settings across multiple tabs
- **Real-time Status**: Live status updates and error reporting

## Installation

### Prerequisites
- Python 3.7 or higher
- Windows operating system (required for game interaction)
- Tantra MMORPG client

### Setup
1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the bot:
   ```bash
   python tantra_bot.py
   ```

## Usage

### First Time Setup
1. **Configure Game Window**: Set the correct window title for your Tantra client
2. **Set Login Credentials**: Enter username, password, and Tantra installation directory
3. **Configure Skills**: Enable desired skills and set appropriate delays
4. **Setup Healing**: Configure HP/TP thresholds and healing methods
5. **Monster List**: Add target monsters to the hunting list

### Running the Bot
1. Start Tantra and log into the game
2. Position your character in a suitable hunting area
3. Press **F8** or click "Start Bot" to begin automation
4. Use **F10** to pause/resume or the pause checkbox
5. Press **F9** for trade spam mode
6. Press **F11** for left-click automation

### Hotkeys
- **F5**: Get current mouse position (for Silfrijan coordinates)
- **F8**: Start/Stop bot
- **F9**: Trade spam mode
- **F10**: Pause/Resume bot
- **F11**: Auto left-clicker
- **Shift+Alt+M**: Show main menu
- **Shift+Alt+A**: Show auto-login menu

## Configuration

### Skills Tab
- Enable/disable individual skills (1-4)
- Set skill delays in milliseconds
- Configure R key usage (disable for Mage classes)
- Setup auto-looting with F key

### Buffs Tab
- Configure F2 function key buffs (1-4)
- Set buff durations for automatic recasting
- Buffs are cast using F2+number combinations

### Healing Tab
- **HP Healing**: Use skill 7 for healing at specified HP percentage
- **HP Potions**: Use F1+8 for HP potions at specified percentage
- **TP Potions**: Use F1+9 for TP potions at specified percentage

### Auto Login Tab
- Set login credentials and game directory
- Configure server and character selection
- Adjust timing delays for different connection speeds
- Test connection and perform single login attempts

### Settings Tab
- **Party Settings**: Auto-accept invites with custom messages
- **Trade Settings**: Auto-deny trade requests with custom replies
- **PM Auto-Reply**: Automatic responses to private messages
- **Anti-Stuck**: Movement timing configuration
- **Silfrijan Coordinates**: Self-resurrection positioning

## Important Notes

### Memory Reading Limitation
This Python version currently uses placeholder logic for memory reading functions. The original AutoIt version read game memory directly to:
- Check character HP/TP values
- Detect character death status
- Monitor equipment durability
- Detect dialog boxes and PMs

To fully replicate the original functionality, you would need to:
1. Implement memory reading using ctypes or similar
2. Find current memory addresses for your game version
3. Update the placeholder functions with actual memory operations

### Game Compatibility
- Designed for Tantra MMORPG
- May require adjustments for different game versions
- Memory addresses need to be updated for current game builds

### Safety Features
- Pause functionality to stop automation quickly
- Error handling to prevent crashes
- Configuration validation
- Window detection to ensure game is running

## Troubleshooting

### Common Issues
1. **"Could not find game window"**: Check window title matches exactly
2. **Hotkeys not working**: Run as administrator or check for conflicts
3. **Skills not casting**: Verify skill delays and game responsiveness
4. **Auto-login fails**: Check credentials and timing delays

### Performance Tips
- Adjust skill delays based on your connection speed
- Use anti-stuck features in crowded areas
- Monitor bot performance and adjust settings as needed
- Save configurations for different characters/situations

## Legal Disclaimer

This bot is for educational purposes only. Use at your own risk and in accordance with the game's terms of service. The authors are not responsible for any consequences of using this software, including but not limited to account suspension or termination.

## Credits

- Original AutoIt version by booster101
- Python rebuild with enhanced features and modern GUI
- Based on the Tantra Bot System v1.05

## License

This project is provided as-is for educational purposes. Please respect the original author's work and the game's terms of service.

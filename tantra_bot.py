#!/usr/bin/env python3
"""
Tantra Bot System - Python Version
A comprehensive automation bot for the Tantra MMORPG
Author: Rebuilt from AutoIt version by booster101
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import threading
import time
import json
import os
import sys
import subprocess
import psutil
import win32gui
import win32con
import win32api
import win32process
import ctypes
from ctypes import wintypes
import keyboard
import pyautogui
import configparser

class TantraBot:
    def __init__(self):
        self.version = "2.0.0"
        self.running = False
        self.paused = False
        self.window_title = "Tantra Launcher"
        self.current_dir = os.getcwd()
        
        # Game window handle and process info
        self.game_hwnd = None
        self.game_pid = None
        
        # Bot configuration
        self.config = {
            'skills': {
                'skill1_enabled': True,
                'skill1_delay': 1000,
                'skill2_enabled': True,
                'skill2_delay': 1000,
                'skill3_enabled': True,
                'skill3_delay': 1000,
                'skill4_enabled': True,
                'skill4_delay': 1000,
                'use_r_key': True,
                'loot_enabled': True,
                'loot_delay': 500
            },
            'buffs': {
                'buff1_enabled': False,
                'buff1_duration': 300000,
                'buff2_enabled': False,
                'buff2_duration': 300000,
                'buff3_enabled': False,
                'buff3_duration': 300000,
                'buff4_enabled': False,
                'buff4_duration': 300000
            },
            'healing': {
                'heal_skill_enabled': True,
                'heal_percentage': 30,
                'heal_delay': 1000,
                'potion_enabled': True,
                'potion_percentage': 50,
                'potion_delay': 1000,
                'tp_potion_enabled': True,
                'tp_percentage': 30,
                'tp_delay': 1000
            },
            'auto_features': {
                'auto_login': False,
                'auto_repair': False,
                'auto_silf': False,
                'silf_x': 150,
                'silf_y': 500,
                'anti_stuck': True,
                'stuck_spin_time': 1000,
                'stuck_move_time': 1000
            },
            'login': {
                'username': '',
                'password': '',
                'tantra_dir': 'C:\\Program Files\\Tantra\\',
                'server_selection': 1,
                'character_selection': 1,
                'delays': {
                    'wait_for_tantra': 3000,
                    'wait_for_login': 25000,
                    'server_select': 5000,
                    'character_select': 5000,
                    'enter_game': 5000
                }
            },
            'monster_list': ['ANTI AVARA CARA', 'Kemsa Tonyo', 'Mosa Tonyo'],
            'party': {
                'auto_accept': False,
                'join_reply': 'Thanks for invite!',
                'deny_reply': 'Sorry, busy right now'
            },
            'trade': {
                'auto_deny': True,
                'deny_reply': 'Sorry am busy'
            },
            'pm_reply': {
                'enabled': False,
                'reply_text': 'I am currently busy, please try again later.'
            }
        }
        
        # Timing variables
        self.last_buff_times = [0, 0, 0, 0]
        self.last_heal_time = 0
        self.last_target_time = 0
        
        # Memory addresses (these would need to be updated for current game version)
        self.memory_addresses = {
            'character_base': 0x00000000,  # Placeholder - needs actual addresses
            'hp_current_offset': 0x100,
            'hp_max_offset': 0x104,
            'tp_current_offset': 0x108,
            'tp_max_offset': 0x10C,
            'target_base': 0x00000000,
            'dialog_base': 0x00000000
        }
        
        self.setup_gui()
        self.setup_hotkeys()
        
    def setup_gui(self):
        """Initialize the main GUI"""
        self.root = tk.Tk()
        self.root.title(f"Tantra Bot System v{self.version}")
        self.root.geometry("500x600")
        self.root.resizable(False, False)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Create tabs
        self.create_main_tab()
        self.create_skills_tab()
        self.create_buffs_tab()
        self.create_healing_tab()
        self.create_login_tab()
        self.create_settings_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Press F8 to start bot")
        self.status_bar = tk.Label(self.root, textvariable=self.status_var, 
                                 relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Control buttons
        self.create_control_buttons()
        
    def create_main_tab(self):
        """Create the main control tab"""
        main_frame = ttk.Frame(self.notebook)
        self.notebook.add(main_frame, text="Main")
        
        # Window title
        ttk.Label(main_frame, text="Game Window Title:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.window_title_var = tk.StringVar(value=self.window_title)
        ttk.Entry(main_frame, textvariable=self.window_title_var, width=30).grid(row=0, column=1, padx=5, pady=5)
        
        # Monster list
        ttk.Label(main_frame, text="Target Monsters:").grid(row=1, column=0, sticky='nw', padx=5, pady=5)
        self.monster_listbox = tk.Listbox(main_frame, height=6, width=30)
        self.monster_listbox.grid(row=1, column=1, padx=5, pady=5)
        
        # Monster list buttons
        monster_btn_frame = ttk.Frame(main_frame)
        monster_btn_frame.grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(monster_btn_frame, text="Add", command=self.add_monster).pack(side='left', padx=2)
        ttk.Button(monster_btn_frame, text="Remove", command=self.remove_monster).pack(side='left', padx=2)
        ttk.Button(monster_btn_frame, text="Clear", command=self.clear_monsters).pack(side='left', padx=2)
        
        # Auto features
        auto_frame = ttk.LabelFrame(main_frame, text="Auto Features")
        auto_frame.grid(row=3, column=0, columnspan=2, sticky='ew', padx=5, pady=5)
        
        self.auto_login_var = tk.BooleanVar(value=self.config['auto_features']['auto_login'])
        ttk.Checkbutton(auto_frame, text="Auto Login", variable=self.auto_login_var).grid(row=0, column=0, sticky='w')
        
        self.auto_repair_var = tk.BooleanVar(value=self.config['auto_features']['auto_repair'])
        ttk.Checkbutton(auto_frame, text="Auto Repair", variable=self.auto_repair_var).grid(row=0, column=1, sticky='w')
        
        self.auto_silf_var = tk.BooleanVar(value=self.config['auto_features']['auto_silf'])
        ttk.Checkbutton(auto_frame, text="Auto Silfrijan (Self Resurrect)", variable=self.auto_silf_var).grid(row=1, column=0, columnspan=2, sticky='w')
        
        self.anti_stuck_var = tk.BooleanVar(value=self.config['auto_features']['anti_stuck'])
        ttk.Checkbutton(auto_frame, text="Anti-Stuck (Turn/Move when stuck)", variable=self.anti_stuck_var).grid(row=2, column=0, columnspan=2, sticky='w')
        
        # Load initial monster list
        for monster in self.config['monster_list']:
            self.monster_listbox.insert(tk.END, monster)
            
    def create_skills_tab(self):
        """Create the skills configuration tab"""
        skills_frame = ttk.Frame(self.notebook)
        self.notebook.add(skills_frame, text="Skills")
        
        # Skills configuration
        ttk.Label(skills_frame, text="Skill", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(skills_frame, text="Enabled", font=('Arial', 10, 'bold')).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(skills_frame, text="Delay (ms)", font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=5, pady=5)
        
        # Skill 1-4
        self.skill_vars = []
        self.skill_delay_vars = []
        
        for i in range(4):
            skill_num = i + 1
            ttk.Label(skills_frame, text=f"Skill {skill_num} (Key {skill_num})").grid(row=skill_num, column=0, sticky='w', padx=5, pady=2)
            
            var = tk.BooleanVar(value=self.config['skills'][f'skill{skill_num}_enabled'])
            self.skill_vars.append(var)
            ttk.Checkbutton(skills_frame, variable=var).grid(row=skill_num, column=1, padx=5, pady=2)
            
            delay_var = tk.StringVar(value=str(self.config['skills'][f'skill{skill_num}_delay']))
            self.skill_delay_vars.append(delay_var)
            ttk.Entry(skills_frame, textvariable=delay_var, width=10).grid(row=skill_num, column=2, padx=5, pady=2)
        
        # Additional skill options
        ttk.Separator(skills_frame, orient='horizontal').grid(row=5, column=0, columnspan=3, sticky='ew', pady=10)
        
        self.use_r_var = tk.BooleanVar(value=self.config['skills']['use_r_key'])
        ttk.Checkbutton(skills_frame, text="Use R key (uncheck for Mage)", variable=self.use_r_var).grid(row=6, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        
        self.loot_var = tk.BooleanVar(value=self.config['skills']['loot_enabled'])
        ttk.Checkbutton(skills_frame, text="Auto Loot (F key)", variable=self.loot_var).grid(row=7, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        
        self.loot_delay_var = tk.StringVar(value=str(self.config['skills']['loot_delay']))
        ttk.Label(skills_frame, text="Loot Delay (ms):").grid(row=8, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(skills_frame, textvariable=self.loot_delay_var, width=10).grid(row=8, column=1, padx=5, pady=2)
        
    def create_buffs_tab(self):
        """Create the buffs configuration tab"""
        buffs_frame = ttk.Frame(self.notebook)
        self.notebook.add(buffs_frame, text="Buffs")
        
        ttk.Label(buffs_frame, text="Buff", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(buffs_frame, text="Enabled", font=('Arial', 10, 'bold')).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(buffs_frame, text="Duration (ms)", font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=5, pady=5)
        
        self.buff_vars = []
        self.buff_duration_vars = []
        
        for i in range(4):
            buff_num = i + 1
            ttk.Label(buffs_frame, text=f"Buff {buff_num} (F2+{buff_num})").grid(row=buff_num, column=0, sticky='w', padx=5, pady=2)
            
            var = tk.BooleanVar(value=self.config['buffs'][f'buff{buff_num}_enabled'])
            self.buff_vars.append(var)
            ttk.Checkbutton(buffs_frame, variable=var).grid(row=buff_num, column=1, padx=5, pady=2)
            
            duration_var = tk.StringVar(value=str(self.config['buffs'][f'buff{buff_num}_duration']))
            self.buff_duration_vars.append(duration_var)
            ttk.Entry(buffs_frame, textvariable=duration_var, width=10).grid(row=buff_num, column=2, padx=5, pady=2)
            
        ttk.Label(buffs_frame, text="Note: Buffs are cast using F2 function keys", 
                 font=('Arial', 9, 'italic')).grid(row=5, column=0, columnspan=3, pady=10)
                 
    def create_healing_tab(self):
        """Create the healing configuration tab"""
        healing_frame = ttk.Frame(self.notebook)
        self.notebook.add(healing_frame, text="Healing")
        
        # HP Healing
        hp_frame = ttk.LabelFrame(healing_frame, text="HP Healing")
        hp_frame.pack(fill='x', padx=5, pady=5)
        
        self.heal_skill_var = tk.BooleanVar(value=self.config['healing']['heal_skill_enabled'])
        ttk.Checkbutton(hp_frame, text="Use Heal Skill (Key 7)", variable=self.heal_skill_var).grid(row=0, column=0, sticky='w', padx=5, pady=2)
        
        ttk.Label(hp_frame, text="Heal at HP %:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.heal_percentage_var = tk.StringVar(value=str(self.config['healing']['heal_percentage']))
        ttk.Entry(hp_frame, textvariable=self.heal_percentage_var, width=10).grid(row=1, column=1, padx=5, pady=2)
        
        # HP Potions
        self.potion_var = tk.BooleanVar(value=self.config['healing']['potion_enabled'])
        ttk.Checkbutton(hp_frame, text="Use HP Potions (Key 8)", variable=self.potion_var).grid(row=2, column=0, sticky='w', padx=5, pady=2)
        
        ttk.Label(hp_frame, text="Potion at HP %:").grid(row=3, column=0, sticky='w', padx=5, pady=2)
        self.potion_percentage_var = tk.StringVar(value=str(self.config['healing']['potion_percentage']))
        ttk.Entry(hp_frame, textvariable=self.potion_percentage_var, width=10).grid(row=3, column=1, padx=5, pady=2)
        
        # TP Potions
        tp_frame = ttk.LabelFrame(healing_frame, text="TP (Mana) Management")
        tp_frame.pack(fill='x', padx=5, pady=5)
        
        self.tp_potion_var = tk.BooleanVar(value=self.config['healing']['tp_potion_enabled'])
        ttk.Checkbutton(tp_frame, text="Use TP Potions (Key 9)", variable=self.tp_potion_var).grid(row=0, column=0, sticky='w', padx=5, pady=2)
        
        ttk.Label(tp_frame, text="TP Potion at %:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.tp_percentage_var = tk.StringVar(value=str(self.config['healing']['tp_percentage']))
        ttk.Entry(tp_frame, textvariable=self.tp_percentage_var, width=10).grid(row=1, column=1, padx=5, pady=2)

    def create_login_tab(self):
        """Create the auto-login configuration tab"""
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="Auto Login")

        # Login credentials
        cred_frame = ttk.LabelFrame(login_frame, text="Login Credentials")
        cred_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(cred_frame, text="Username:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.username_var = tk.StringVar(value=self.config['login']['username'])
        ttk.Entry(cred_frame, textvariable=self.username_var, width=20).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(cred_frame, text="Password:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.password_var = tk.StringVar(value=self.config['login']['password'])
        ttk.Entry(cred_frame, textvariable=self.password_var, width=20, show='*').grid(row=1, column=1, padx=5, pady=2)

        ttk.Label(cred_frame, text="Tantra Directory:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        self.tantra_dir_var = tk.StringVar(value=self.config['login']['tantra_dir'])
        dir_frame = ttk.Frame(cred_frame)
        dir_frame.grid(row=2, column=1, sticky='ew', padx=5, pady=2)
        ttk.Entry(dir_frame, textvariable=self.tantra_dir_var, width=15).pack(side='left')
        ttk.Button(dir_frame, text="Browse", command=self.browse_tantra_dir).pack(side='left', padx=(5,0))

        # Selection options
        select_frame = ttk.LabelFrame(login_frame, text="Game Selection")
        select_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(select_frame, text="Server:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.server_var = tk.StringVar(value=str(self.config['login']['server_selection']))
        ttk.Entry(select_frame, textvariable=self.server_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(select_frame, text="Character:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.character_var = tk.StringVar(value=str(self.config['login']['character_selection']))
        ttk.Entry(select_frame, textvariable=self.character_var, width=10).grid(row=1, column=1, padx=5, pady=2)

        # Timing delays
        delay_frame = ttk.LabelFrame(login_frame, text="Login Delays (milliseconds)")
        delay_frame.pack(fill='x', padx=5, pady=5)

        delays = [
            ("Wait for Tantra:", 'wait_for_tantra'),
            ("Wait for Login:", 'wait_for_login'),
            ("Server Select:", 'server_select'),
            ("Character Select:", 'character_select'),
            ("Enter Game:", 'enter_game')
        ]

        self.delay_vars = {}
        for i, (label, key) in enumerate(delays):
            ttk.Label(delay_frame, text=label).grid(row=i, column=0, sticky='w', padx=5, pady=2)
            var = tk.StringVar(value=str(self.config['login']['delays'][key]))
            self.delay_vars[key] = var
            ttk.Entry(delay_frame, textvariable=var, width=10).grid(row=i, column=1, padx=5, pady=2)

        # Login buttons
        btn_frame = ttk.Frame(login_frame)
        btn_frame.pack(fill='x', padx=5, pady=10)
        ttk.Button(btn_frame, text="Login Once", command=self.login_once).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Test Connection", command=self.test_connection).pack(side='left', padx=5)

    def create_settings_tab(self):
        """Create the settings and advanced options tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Settings")

        # Party settings
        party_frame = ttk.LabelFrame(settings_frame, text="Party Settings")
        party_frame.pack(fill='x', padx=5, pady=5)

        self.party_accept_var = tk.BooleanVar(value=self.config['party']['auto_accept'])
        ttk.Checkbutton(party_frame, text="Auto Accept Party Invites", variable=self.party_accept_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(party_frame, text="Join Reply:").pack(anchor='w', padx=5)
        self.party_join_var = tk.StringVar(value=self.config['party']['join_reply'])
        ttk.Entry(party_frame, textvariable=self.party_join_var, width=40).pack(anchor='w', padx=5, pady=2)

        ttk.Label(party_frame, text="Deny Reply:").pack(anchor='w', padx=5)
        self.party_deny_var = tk.StringVar(value=self.config['party']['deny_reply'])
        ttk.Entry(party_frame, textvariable=self.party_deny_var, width=40).pack(anchor='w', padx=5, pady=2)

        # Trade settings
        trade_frame = ttk.LabelFrame(settings_frame, text="Trade Settings")
        trade_frame.pack(fill='x', padx=5, pady=5)

        self.trade_deny_var = tk.BooleanVar(value=self.config['trade']['auto_deny'])
        ttk.Checkbutton(trade_frame, text="Auto Deny Trade Requests", variable=self.trade_deny_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(trade_frame, text="Deny Reply:").pack(anchor='w', padx=5)
        self.trade_reply_var = tk.StringVar(value=self.config['trade']['deny_reply'])
        ttk.Entry(trade_frame, textvariable=self.trade_reply_var, width=40).pack(anchor='w', padx=5, pady=2)

        # PM Reply settings
        pm_frame = ttk.LabelFrame(settings_frame, text="Private Message Auto-Reply")
        pm_frame.pack(fill='x', padx=5, pady=5)

        self.pm_reply_enabled_var = tk.BooleanVar(value=self.config['pm_reply']['enabled'])
        ttk.Checkbutton(pm_frame, text="Enable Auto-Reply to PMs", variable=self.pm_reply_enabled_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(pm_frame, text="Reply Text:").pack(anchor='w', padx=5)
        self.pm_reply_text_var = tk.StringVar(value=self.config['pm_reply']['reply_text'])
        ttk.Entry(pm_frame, textvariable=self.pm_reply_text_var, width=40).pack(anchor='w', padx=5, pady=2)

        # Anti-stuck settings
        stuck_frame = ttk.LabelFrame(settings_frame, text="Anti-Stuck Settings")
        stuck_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(stuck_frame, text="Spin Time (ms):").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.stuck_spin_var = tk.StringVar(value=str(self.config['auto_features']['stuck_spin_time']))
        ttk.Entry(stuck_frame, textvariable=self.stuck_spin_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(stuck_frame, text="Move Time (ms):").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.stuck_move_var = tk.StringVar(value=str(self.config['auto_features']['stuck_move_time']))
        ttk.Entry(stuck_frame, textvariable=self.stuck_move_var, width=10).grid(row=1, column=1, padx=5, pady=2)

        # Silfrijan coordinates
        silf_frame = ttk.LabelFrame(settings_frame, text="Silfrijan Coordinates")
        silf_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(silf_frame, text="X:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.silf_x_var = tk.StringVar(value=str(self.config['auto_features']['silf_x']))
        ttk.Entry(silf_frame, textvariable=self.silf_x_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(silf_frame, text="Y:").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        self.silf_y_var = tk.StringVar(value=str(self.config['auto_features']['silf_y']))
        ttk.Entry(silf_frame, textvariable=self.silf_y_var, width=10).grid(row=0, column=3, padx=5, pady=2)

        ttk.Button(silf_frame, text="Get Current Position (F5)", command=self.get_mouse_position).grid(row=1, column=0, columnspan=4, pady=5)

    def create_control_buttons(self):
        """Create the main control buttons"""
        control_frame = ttk.Frame(self.root)
        control_frame.pack(side=tk.BOTTOM, fill='x', padx=5, pady=5)

        self.start_btn = ttk.Button(control_frame, text="Start Bot (F8)", command=self.start_bot)
        self.start_btn.pack(side='left', padx=5)

        self.pause_btn = ttk.Button(control_frame, text="Pause (F10)", command=self.pause_bot, state='disabled')
        self.pause_btn.pack(side='left', padx=5)

        self.stop_btn = ttk.Button(control_frame, text="Stop", command=self.stop_bot, state='disabled')
        self.stop_btn.pack(side='left', padx=5)

        ttk.Button(control_frame, text="Save Config", command=self.save_config).pack(side='right', padx=5)
        ttk.Button(control_frame, text="Load Config", command=self.load_config).pack(side='right', padx=5)

        # Pause checkbox
        self.pause_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="Pause Bot", variable=self.pause_var,
                       command=self.toggle_pause).pack(side='right', padx=10)

    def setup_hotkeys(self):
        """Setup global hotkeys"""
        try:
            keyboard.add_hotkey('f8', self.start_bot)
            keyboard.add_hotkey('f9', self.trade_spam)
            keyboard.add_hotkey('f10', self.pause_bot)
            keyboard.add_hotkey('f11', self.left_clicker)
            keyboard.add_hotkey('f5', self.get_mouse_position)
            keyboard.add_hotkey('shift+alt+m', self.show_main_menu)
            keyboard.add_hotkey('shift+alt+a', self.show_login_menu)
        except Exception as e:
            print(f"Warning: Could not set up hotkeys: {e}")

    # GUI Event Handlers
    def add_monster(self):
        """Add a monster to the target list"""
        monster_name = simpledialog.askstring("Add Monster", "Enter monster name:")
        if monster_name:
            self.monster_listbox.insert(tk.END, monster_name)
            self.config['monster_list'].append(monster_name)

    def remove_monster(self):
        """Remove selected monster from the list"""
        selection = self.monster_listbox.curselection()
        if selection:
            index = selection[0]
            self.monster_listbox.delete(index)
            if index < len(self.config['monster_list']):
                del self.config['monster_list'][index]

    def clear_monsters(self):
        """Clear all monsters from the list"""
        self.monster_listbox.delete(0, tk.END)
        self.config['monster_list'].clear()

    def browse_tantra_dir(self):
        """Browse for Tantra installation directory"""
        directory = filedialog.askdirectory(title="Select Tantra Installation Directory")
        if directory:
            self.tantra_dir_var.set(directory)

    def get_mouse_position(self):
        """Get current mouse position for Silfrijan coordinates"""
        x, y = pyautogui.position()
        self.silf_x_var.set(str(x))
        self.silf_y_var.set(str(y))
        messagebox.showinfo("Position Set", f"Silfrijan position set to: {x}, {y}")

    def toggle_pause(self):
        """Toggle bot pause state"""
        self.paused = self.pause_var.get()
        if self.paused:
            self.status_var.set("Bot Paused")
        else:
            self.status_var.set("Bot Running")

    # Bot Control Methods
    def start_bot(self):
        """Start the bot"""
        if not self.running:
            self.update_config_from_gui()

            # Find game window
            if not self.find_game_window():
                messagebox.showerror("Error", "Could not find Tantra game window!")
                return

            self.running = True
            self.paused = False
            self.start_btn.config(state='disabled')
            self.pause_btn.config(state='normal')
            self.stop_btn.config(state='normal')
            self.status_var.set("Bot Started")

            # Start bot thread
            self.bot_thread = threading.Thread(target=self.bot_main_loop, daemon=True)
            self.bot_thread.start()

    def pause_bot(self):
        """Pause/unpause the bot"""
        if self.running:
            self.paused = not self.paused
            if self.paused:
                self.status_var.set("Bot Paused")
                self.pause_btn.config(text="Resume (F10)")
            else:
                self.status_var.set("Bot Running")
                self.pause_btn.config(text="Pause (F10)")

    def stop_bot(self):
        """Stop the bot"""
        self.running = False
        self.paused = False
        self.start_btn.config(state='normal')
        self.pause_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.pause_btn.config(text="Pause (F10)")
        self.status_var.set("Bot Stopped")

    def find_game_window(self):
        """Find the game window handle"""
        try:
            self.game_hwnd = win32gui.FindWindow(None, self.window_title_var.get())
            if self.game_hwnd:
                _, self.game_pid = win32process.GetWindowThreadProcessId(self.game_hwnd)
                return True
        except Exception as e:
            print(f"Error finding game window: {e}")
        return False

    def send_key_to_game(self, key, hold_time=0.1):
        """Send a key press to the game window"""
        if not self.game_hwnd:
            return

        try:
            # Bring window to foreground
            win32gui.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.05)

            # Send key
            if isinstance(key, str) and len(key) == 1:
                vk_code = ord(key.upper())
            else:
                # Handle special keys
                key_map = {
                    'enter': 0x0D, 'tab': 0x09, 'space': 0x20,
                    'f1': 0x70, 'f2': 0x71, 'f8': 0x77, 'f10': 0x79,
                    'a': 0x41, 'w': 0x57, 'r': 0x52, 'f': 0x46,
                    '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
                    '7': 0x37, '8': 0x38, '9': 0x39
                }
                vk_code = key_map.get(key.lower(), 0x20)

            # Send key down
            win32api.PostMessage(self.game_hwnd, win32con.WM_KEYDOWN, vk_code, 0)
            time.sleep(hold_time)
            # Send key up
            win32api.PostMessage(self.game_hwnd, win32con.WM_KEYUP, vk_code, 0)

        except Exception as e:
            print(f"Error sending key {key}: {e}")

    def click_in_game(self, x, y):
        """Click at specific coordinates in the game window"""
        if not self.game_hwnd:
            return

        try:
            # Convert to window coordinates
            lParam = win32api.MAKELONG(x, y)
            win32api.PostMessage(self.game_hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
            time.sleep(0.05)
            win32api.PostMessage(self.game_hwnd, win32con.WM_LBUTTONUP, 0, lParam)
        except Exception as e:
            print(f"Error clicking at {x}, {y}: {e}")

    # Bot Logic Methods
    def bot_main_loop(self):
        """Main bot execution loop"""
        while self.running:
            if self.paused:
                time.sleep(0.1)
                continue

            try:
                # Check if game window still exists
                if not win32gui.IsWindow(self.game_hwnd):
                    if self.config['auto_features']['auto_login']:
                        self.auto_login()
                    else:
                        self.status_var.set("Game window lost!")
                        break

                # Main bot cycle
                self.check_and_repair()
                self.auto_silfrijan()
                self.reply_to_pm()
                self.cast_buffs()
                self.check_dialogs()
                self.find_target()
                self.use_skills()
                self.auto_loot()
                self.check_healing()

                # Small delay between cycles
                time.sleep(0.1)

            except Exception as e:
                print(f"Error in bot loop: {e}")
                time.sleep(1)

        self.stop_bot()

    def find_target(self):
        """Find and target a monster"""
        if time.time() - self.last_target_time < 1:  # Don't spam targeting
            return

        try:
            if self.config['skills']['use_r_key']:
                self.send_key_to_game('r')
            else:
                # Send 'te' command for targeting
                self.send_key_to_game('t')
                time.sleep(0.05)
                self.send_key_to_game('e')
                time.sleep(0.05)
                self.send_key_to_game('enter')

            self.last_target_time = time.time()

        except Exception as e:
            print(f"Error in find_target: {e}")

    def use_skills(self):
        """Use combat skills"""
        try:
            for i in range(4):
                if self.paused or not self.running:
                    break

                skill_num = i + 1
                if self.config['skills'][f'skill{skill_num}_enabled']:
                    self.send_key_to_game(str(skill_num))
                    delay = self.config['skills'][f'skill{skill_num}_delay'] / 1000.0
                    time.sleep(delay)

        except Exception as e:
            print(f"Error in use_skills: {e}")

    def auto_loot(self):
        """Automatically loot items"""
        if self.config['skills']['loot_enabled']:
            try:
                self.send_key_to_game('f')
                delay = self.config['skills']['loot_delay'] / 1000.0
                time.sleep(delay)
            except Exception as e:
                print(f"Error in auto_loot: {e}")

    def cast_buffs(self):
        """Cast buffs when needed"""
        current_time = time.time() * 1000  # Convert to milliseconds

        try:
            for i in range(4):
                buff_num = i + 1
                if self.config['buffs'][f'buff{buff_num}_enabled']:
                    duration = self.config['buffs'][f'buff{buff_num}_duration']

                    if current_time - self.last_buff_times[i] >= duration:
                        # Switch to F2 mode
                        self.send_key_to_game('f2')
                        time.sleep(0.1)

                        # Cast buff
                        self.send_key_to_game(str(buff_num))
                        time.sleep(0.1)

                        # Switch back to F1 mode
                        self.send_key_to_game('f1')

                        self.last_buff_times[i] = current_time
                        time.sleep(2)  # Wait between buffs

        except Exception as e:
            print(f"Error in cast_buffs: {e}")

    def check_healing(self):
        """Check and perform healing actions"""
        try:
            # This would need actual memory reading to get HP/TP values
            # For now, we'll use placeholder logic

            # HP Healing (Key 7)
            if self.config['healing']['heal_skill_enabled']:
                # Placeholder: assume we need healing
                self.send_key_to_game('7')
                delay = self.config['healing']['heal_delay'] / 1000.0
                time.sleep(delay)

            # HP Potions (Key 8)
            if self.config['healing']['potion_enabled']:
                # Placeholder: assume we need potions
                self.send_key_to_game('f1')
                time.sleep(0.05)
                self.send_key_to_game('8')
                delay = self.config['healing']['potion_delay'] / 1000.0
                time.sleep(delay)

            # TP Potions (Key 9)
            if self.config['healing']['tp_potion_enabled']:
                # Placeholder: assume we need TP
                self.send_key_to_game('f1')
                time.sleep(0.05)
                self.send_key_to_game('9')
                delay = self.config['healing']['tp_delay'] / 1000.0
                time.sleep(delay)

        except Exception as e:
            print(f"Error in check_healing: {e}")

    def auto_silfrijan(self):
        """Auto-resurrect using Silfrijan"""
        if not self.config['auto_features']['auto_silf']:
            return

        try:
            # This would need memory reading to check if character is dead
            # For now, placeholder logic

            # Click on Silfrijan position
            x = self.config['auto_features']['silf_x']
            y = self.config['auto_features']['silf_y']
            self.click_in_game(x, y)
            time.sleep(0.5)

            # Click OK or confirm
            self.click_in_game(515, 370)
            time.sleep(0.5)

        except Exception as e:
            print(f"Error in auto_silfrijan: {e}")

    def check_and_repair(self):
        """Check equipment durability and repair if needed"""
        if not self.config['auto_features']['auto_repair']:
            return

        try:
            # This would need memory reading to check equipment durability
            # For now, placeholder logic

            # Switch to F2 mode and use repair skill (slot 9)
            self.send_key_to_game('f2')
            time.sleep(0.1)
            self.send_key_to_game('9')
            time.sleep(0.1)
            self.send_key_to_game('f1')

        except Exception as e:
            print(f"Error in check_and_repair: {e}")

    def check_dialogs(self):
        """Check and handle various game dialogs"""
        try:
            # Party invitations
            if self.config['party']['auto_accept']:
                # This would need memory reading to detect party dialogs
                # Placeholder logic for accepting party
                pass
            else:
                # Deny party invitation
                pass

            # Trade requests
            if self.config['trade']['auto_deny']:
                # This would need memory reading to detect trade dialogs
                # Placeholder logic for denying trade
                pass

        except Exception as e:
            print(f"Error in check_dialogs: {e}")

    def reply_to_pm(self):
        """Auto-reply to private messages"""
        if not self.config['pm_reply']['enabled']:
            return

        try:
            # This would need memory reading to detect new PMs
            # Placeholder logic for PM reply

            # Send reply command
            self.send_key_to_game('v')
            time.sleep(0.1)

            # Type reply message (would need proper text input)
            reply_text = self.config['pm_reply']['reply_text']
            # For now, just send enter
            self.send_key_to_game('enter')

        except Exception as e:
            print(f"Error in reply_to_pm: {e}")

    def anti_stuck_movement(self):
        """Perform anti-stuck movement"""
        if not self.config['auto_features']['anti_stuck']:
            return

        try:
            # Spin movement
            self.send_key_to_game('a', self.config['auto_features']['stuck_spin_time'] / 1000.0)

            # Forward movement
            self.send_key_to_game('w', self.config['auto_features']['stuck_move_time'] / 1000.0)

        except Exception as e:
            print(f"Error in anti_stuck_movement: {e}")

    # Auto-login functionality
    def auto_login(self):
        """Perform automatic login sequence"""
        try:
            self.status_var.set("Starting auto-login...")

            # Launch Tantra
            tantra_path = os.path.join(self.config['login']['tantra_dir'], 'Tantra.exe')
            if os.path.exists(tantra_path):
                subprocess.Popen(tantra_path)

                # Wait for game to load
                time.sleep(self.config['login']['delays']['wait_for_tantra'] / 1000.0)

                # Find game window
                if self.find_game_window():
                    self.perform_login_sequence()
                else:
                    self.status_var.set("Could not find game window after launch")
            else:
                self.status_var.set("Tantra.exe not found!")

        except Exception as e:
            print(f"Error in auto_login: {e}")
            self.status_var.set(f"Auto-login error: {e}")

    def perform_login_sequence(self):
        """Perform the actual login sequence"""
        try:
            # Wait for login screen
            time.sleep(self.config['login']['delays']['wait_for_login'] / 1000.0)

            # Click on username field and enter credentials
            self.click_in_game(520, 440)
            time.sleep(0.1)

            # Clear field and enter username
            for _ in range(15):
                self.send_key_to_game('backspace')

            # Type username (simplified - would need proper text input)
            username = self.config['login']['username']
            for char in username:
                self.send_key_to_game(char)
                time.sleep(0.05)

            # Tab to password field
            self.send_key_to_game('tab')
            time.sleep(0.1)

            # Clear and enter password
            for _ in range(15):
                self.send_key_to_game('backspace')

            password = self.config['login']['password']
            for char in password:
                self.send_key_to_game(char)
                time.sleep(0.05)

            # Press enter to login
            self.send_key_to_game('enter')

            # Wait and select server
            time.sleep(self.config['login']['delays']['server_select'] / 1000.0)
            self.select_server()

            # Wait and select character
            time.sleep(self.config['login']['delays']['character_select'] / 1000.0)
            self.select_character()

            # Enter game
            time.sleep(self.config['login']['delays']['enter_game'] / 1000.0)
            self.send_key_to_game('enter')

            self.status_var.set("Auto-login completed")

        except Exception as e:
            print(f"Error in perform_login_sequence: {e}")

    def select_server(self):
        """Select game server"""
        try:
            server_num = self.config['login']['server_selection']
            # Click on server (simplified coordinates)
            server_y = 200 + (server_num - 1) * 20
            self.click_in_game(478, server_y)
            time.sleep(0.1)
            self.send_key_to_game('enter')
        except Exception as e:
            print(f"Error in select_server: {e}")

    def select_character(self):
        """Select game character"""
        try:
            char_num = self.config['login']['character_selection']
            # Click on character (simplified coordinates)
            char_y = 200 + (char_num - 1) * 30
            self.click_in_game(400, char_y)
            time.sleep(0.1)
            self.send_key_to_game('enter')
        except Exception as e:
            print(f"Error in select_character: {e}")

    # Additional bot features
    def trade_spam(self):
        """Spam trade messages (F9 hotkey)"""
        try:
            self.send_key_to_game('f2')
            time.sleep(0.1)
            self.send_key_to_game('6')
            time.sleep(0.1)
            self.send_key_to_game('enter')
        except Exception as e:
            print(f"Error in trade_spam: {e}")

    def left_clicker(self):
        """Auto left clicker (F11 hotkey)"""
        try:
            if self.game_hwnd:
                x, y = pyautogui.position()
                self.click_in_game(x, y)
        except Exception as e:
            print(f"Error in left_clicker: {e}")

    def login_once(self):
        """Perform single login attempt"""
        if not self.config['login']['username'] or not self.config['login']['password']:
            messagebox.showerror("Error", "Please enter username and password!")
            return

        threading.Thread(target=self.auto_login, daemon=True).start()

    def test_connection(self):
        """Test internet connection"""
        try:
            import urllib.request
            urllib.request.urlopen('http://www.google.com', timeout=5)
            messagebox.showinfo("Connection Test", "Internet connection is working!")
        except:
            messagebox.showerror("Connection Test", "No internet connection!")

    # Menu functions
    def show_main_menu(self):
        """Show main menu (hotkey function)"""
        self.root.deiconify()
        self.root.lift()

    def show_login_menu(self):
        """Show login tab (hotkey function)"""
        self.show_main_menu()
        self.notebook.select(4)  # Select login tab

    # Configuration management
    def update_config_from_gui(self):
        """Update configuration from GUI values"""
        try:
            # Update window title
            self.window_title = self.window_title_var.get()

            # Update skills
            for i in range(4):
                skill_num = i + 1
                self.config['skills'][f'skill{skill_num}_enabled'] = self.skill_vars[i].get()
                self.config['skills'][f'skill{skill_num}_delay'] = int(self.skill_delay_vars[i].get())

            self.config['skills']['use_r_key'] = self.use_r_var.get()
            self.config['skills']['loot_enabled'] = self.loot_var.get()
            self.config['skills']['loot_delay'] = int(self.loot_delay_var.get())

            # Update buffs
            for i in range(4):
                buff_num = i + 1
                self.config['buffs'][f'buff{buff_num}_enabled'] = self.buff_vars[i].get()
                self.config['buffs'][f'buff{buff_num}_duration'] = int(self.buff_duration_vars[i].get())

            # Update healing
            self.config['healing']['heal_skill_enabled'] = self.heal_skill_var.get()
            self.config['healing']['heal_percentage'] = int(self.heal_percentage_var.get())
            self.config['healing']['potion_enabled'] = self.potion_var.get()
            self.config['healing']['potion_percentage'] = int(self.potion_percentage_var.get())
            self.config['healing']['tp_potion_enabled'] = self.tp_potion_var.get()
            self.config['healing']['tp_percentage'] = int(self.tp_percentage_var.get())

            # Update auto features
            self.config['auto_features']['auto_login'] = self.auto_login_var.get()
            self.config['auto_features']['auto_repair'] = self.auto_repair_var.get()
            self.config['auto_features']['auto_silf'] = self.auto_silf_var.get()
            self.config['auto_features']['silf_x'] = int(self.silf_x_var.get())
            self.config['auto_features']['silf_y'] = int(self.silf_y_var.get())
            self.config['auto_features']['anti_stuck'] = self.anti_stuck_var.get()
            self.config['auto_features']['stuck_spin_time'] = int(self.stuck_spin_var.get())
            self.config['auto_features']['stuck_move_time'] = int(self.stuck_move_var.get())

            # Update login
            self.config['login']['username'] = self.username_var.get()
            self.config['login']['password'] = self.password_var.get()
            self.config['login']['tantra_dir'] = self.tantra_dir_var.get()
            self.config['login']['server_selection'] = int(self.server_var.get())
            self.config['login']['character_selection'] = int(self.character_var.get())

            for key, var in self.delay_vars.items():
                self.config['login']['delays'][key] = int(var.get())

            # Update party/trade/PM settings
            self.config['party']['auto_accept'] = self.party_accept_var.get()
            self.config['party']['join_reply'] = self.party_join_var.get()
            self.config['party']['deny_reply'] = self.party_deny_var.get()
            self.config['trade']['auto_deny'] = self.trade_deny_var.get()
            self.config['trade']['deny_reply'] = self.trade_reply_var.get()
            self.config['pm_reply']['enabled'] = self.pm_reply_enabled_var.get()
            self.config['pm_reply']['reply_text'] = self.pm_reply_text_var.get()

            # Update monster list
            self.config['monster_list'] = list(self.monster_listbox.get(0, tk.END))

        except ValueError as e:
            messagebox.showerror("Configuration Error", f"Invalid value in configuration: {e}")
        except Exception as e:
            print(f"Error updating config: {e}")

    def save_config(self):
        """Save configuration to file"""
        try:
            self.update_config_from_gui()

            filename = filedialog.asksaveasfilename(
                title="Save Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w') as f:
                    json.dump(self.config, f, indent=4)
                messagebox.showinfo("Success", "Configuration saved successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def load_config(self):
        """Load configuration from file"""
        try:
            filename = filedialog.askopenfilename(
                title="Load Configuration",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r') as f:
                    loaded_config = json.load(f)

                # Update config and GUI
                self.config.update(loaded_config)
                self.update_gui_from_config()
                messagebox.showinfo("Success", "Configuration loaded successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def update_gui_from_config(self):
        """Update GUI from current configuration"""
        try:
            # Update window title
            self.window_title_var.set(self.window_title)

            # Update skills
            for i in range(4):
                skill_num = i + 1
                self.skill_vars[i].set(self.config['skills'][f'skill{skill_num}_enabled'])
                self.skill_delay_vars[i].set(str(self.config['skills'][f'skill{skill_num}_delay']))

            self.use_r_var.set(self.config['skills']['use_r_key'])
            self.loot_var.set(self.config['skills']['loot_enabled'])
            self.loot_delay_var.set(str(self.config['skills']['loot_delay']))

            # Update buffs
            for i in range(4):
                buff_num = i + 1
                self.buff_vars[i].set(self.config['buffs'][f'buff{buff_num}_enabled'])
                self.buff_duration_vars[i].set(str(self.config['buffs'][f'buff{buff_num}_duration']))

            # Update healing
            self.heal_skill_var.set(self.config['healing']['heal_skill_enabled'])
            self.heal_percentage_var.set(str(self.config['healing']['heal_percentage']))
            self.potion_var.set(self.config['healing']['potion_enabled'])
            self.potion_percentage_var.set(str(self.config['healing']['potion_percentage']))
            self.tp_potion_var.set(self.config['healing']['tp_potion_enabled'])
            self.tp_percentage_var.set(str(self.config['healing']['tp_percentage']))

            # Update auto features
            self.auto_login_var.set(self.config['auto_features']['auto_login'])
            self.auto_repair_var.set(self.config['auto_features']['auto_repair'])
            self.auto_silf_var.set(self.config['auto_features']['auto_silf'])
            self.silf_x_var.set(str(self.config['auto_features']['silf_x']))
            self.silf_y_var.set(str(self.config['auto_features']['silf_y']))
            self.anti_stuck_var.set(self.config['auto_features']['anti_stuck'])
            self.stuck_spin_var.set(str(self.config['auto_features']['stuck_spin_time']))
            self.stuck_move_var.set(str(self.config['auto_features']['stuck_move_time']))

            # Update login
            self.username_var.set(self.config['login']['username'])
            self.password_var.set(self.config['login']['password'])
            self.tantra_dir_var.set(self.config['login']['tantra_dir'])
            self.server_var.set(str(self.config['login']['server_selection']))
            self.character_var.set(str(self.config['login']['character_selection']))

            for key, var in self.delay_vars.items():
                var.set(str(self.config['login']['delays'][key]))

            # Update party/trade/PM settings
            self.party_accept_var.set(self.config['party']['auto_accept'])
            self.party_join_var.set(self.config['party']['join_reply'])
            self.party_deny_var.set(self.config['party']['deny_reply'])
            self.trade_deny_var.set(self.config['trade']['auto_deny'])
            self.trade_reply_var.set(self.config['trade']['deny_reply'])
            self.pm_reply_enabled_var.set(self.config['pm_reply']['enabled'])
            self.pm_reply_text_var.set(self.config['pm_reply']['reply_text'])

            # Update monster list
            self.monster_listbox.delete(0, tk.END)
            for monster in self.config['monster_list']:
                self.monster_listbox.insert(tk.END, monster)

        except Exception as e:
            print(f"Error updating GUI from config: {e}")

    def run(self):
        """Start the application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()

    def on_closing(self):
        """Handle application closing"""
        if self.running:
            self.stop_bot()
        self.root.quit()
        self.root.destroy()


def main():
    """Main entry point"""
    try:
        # Check if required modules are available
        import tkinter.simpledialog

        # Create and run the bot
        bot = TantraBot()
        bot.run()

    except ImportError as e:
        print(f"Missing required module: {e}")
        print("Please install required packages:")
        print("pip install pyautogui keyboard pywin32")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting bot: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

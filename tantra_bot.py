#!/usr/bin/env python3
"""
Tantra Bot System - Python Version
A comprehensive automation bot for the Tantra MMORPG
Author: Rebuilt from AutoIt version by booster101
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import threading
import time
import json
import os
import sys
import subprocess
import psutil
import win32gui
import win32con
import win32api
import win32process
import ctypes
from ctypes import wintypes
import keyboard
import pyautogui
import configparser
from tkinter import font

class TantraBot:
    def __init__(self):
        self.version = "2.0.0"
        self.running = False
        self.paused = False
        self.window_title = "Tantra Launcher"
        self.current_dir = os.getcwd()
        
        # Game window handle and process info
        self.game_hwnd = None
        self.game_pid = None
        
        # Bot configuration
        self.config = {
            'skills': {
                'skill1_enabled': True,
                'skill1_delay': 1000,
                'skill2_enabled': True,
                'skill2_delay': 1000,
                'skill3_enabled': True,
                'skill3_delay': 1000,
                'skill4_enabled': True,
                'skill4_delay': 1000,
                'use_r_key': True,
                'loot_enabled': True,
                'loot_delay': 500
            },
            'buffs': {
                'buff1_enabled': False,
                'buff1_duration': 300000,
                'buff2_enabled': False,
                'buff2_duration': 300000,
                'buff3_enabled': False,
                'buff3_duration': 300000,
                'buff4_enabled': False,
                'buff4_duration': 300000
            },
            'healing': {
                'heal_skill_enabled': True,
                'heal_percentage': 30,
                'heal_delay': 1000,
                'potion_enabled': True,
                'potion_percentage': 50,
                'potion_delay': 1000,
                'tp_potion_enabled': True,
                'tp_percentage': 30,
                'tp_delay': 1000
            },
            'auto_features': {
                'auto_login': False,
                'auto_repair': False,
                'auto_silf': False,
                'silf_x': 150,
                'silf_y': 500,
                'anti_stuck': True,
                'stuck_spin_time': 1000,
                'stuck_move_time': 1000
            },
            'login': {
                'username': '',
                'password': '',
                'tantra_dir': 'C:\\Program Files\\Tantra\\',
                'server_selection': 1,
                'character_selection': 1,
                'delays': {
                    'wait_for_tantra': 3000,
                    'wait_for_login': 25000,
                    'server_select': 5000,
                    'character_select': 5000,
                    'enter_game': 5000
                }
            },
            'monster_list': ['ANTI AVARA CARA', 'Kemsa Tonyo', 'Mosa Tonyo'],
            'targeting': {
                'priority_targeting': True,
                'target_nearest': False,
                'auto_retarget': True,
                'target_delay': 1000
            },
            'party': {
                'auto_accept': False,
                'join_reply': 'Thanks for invite!',
                'deny_reply': 'Sorry, busy right now'
            },
            'trade': {
                'auto_deny': True,
                'deny_reply': 'Sorry am busy'
            },
            'pm_reply': {
                'enabled': False,
                'reply_text': 'I am currently busy, please try again later.'
            },
            'teleport': {
                'enabled': False,
                'target_address': 0x00000000,
                'search_bytes': '',
                'mandara_coordinates': {'x': 0, 'y': 0}
            },
            'tools': {
                'trade_spam_enabled': False,
                'trade_spam_delay': 1000,
                'trade_spam_message': 'WTS Items! PM me!',
                'left_clicker_enabled': False,
                'left_clicker_delay': 100,
                'connection_monitor': True,
                'auto_answer_quiz': False,
                'quiz_answers_file': 'quiz_answers.txt'
            },
            'advanced': {
                'memory_scan_enabled': False,
                'status_monitoring': True,
                'advanced_anti_stuck': True,
                'stuck_detection_threshold': 5000,
                'repair_threshold': 30.0,
                'auto_reconnect': True,
                'reconnect_delay': 10000
            }
        }
        
        # Timing variables
        self.last_buff_times = [0, 0, 0, 0]
        self.last_heal_time = 0
        self.last_target_time = 0
        self.last_position = {'x': 0, 'y': 0}
        self.stuck_counter = 0
        self.last_damage = 0

        # Tool states
        self.trade_spam_running = False
        self.left_clicker_running = False
        self.connection_lost_time = 0
        
        # Memory addresses (these would need to be updated for current game version)
        self.memory_addresses = {
            'character_base': 0x00000000,  # Placeholder - needs actual addresses
            'hp_current_offset': 0x100,
            'hp_max_offset': 0x104,
            'tp_current_offset': 0x108,
            'tp_max_offset': 0x10C,
            'target_base': 0x00000000,
            'dialog_base': 0x00000000
        }
        
        self.setup_gui()
        self.setup_hotkeys()

    def setup_epic_style(self):
        """Setup INSANE cyberpunk styling"""
        style = ttk.Style()

        # Configure INSANE cyberpunk theme
        style.theme_use('clam')

        # INSANE Notebook styling with neon effects
        style.configure('Epic.TNotebook',
                       background=self.colors['bg_primary'],
                       borderwidth=2,
                       relief='solid',
                       tabmargins=[5, 10, 5, 0])

        style.configure('Epic.TNotebook.Tab',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['neon_cyan'],
                       padding=[25, 15],
                       borderwidth=2,
                       relief='raised',
                       focuscolor='none',
                       font=self.fonts['cyber_header'])

        style.map('Epic.TNotebook.Tab',
                 background=[('selected', self.colors['bg_glow']),
                           ('active', self.colors['bg_accent'])],
                 foreground=[('selected', self.colors['neon_green']),
                           ('active', self.colors['neon_pink'])],
                 relief=[('selected', 'sunken'),
                        ('active', 'raised')])

        # INSANE Frame styling with neon borders
        style.configure('Epic.TFrame',
                       background=self.colors['bg_secondary'],
                       borderwidth=3,
                       relief='ridge')

        style.configure('EpicAccent.TFrame',
                       background=self.colors['bg_glow'],
                       borderwidth=4,
                       relief='groove')

        # INSANE Label styling with cyberpunk colors
        style.configure('Epic.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['neon_cyan'],
                       font=self.fonts['normal'])

        style.configure('EpicTitle.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['neon_green'],
                       font=self.fonts['mega_title'])

        style.configure('EpicHeader.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['neon_orange'],
                       font=self.fonts['cyber_header'])

        # INSANE Button styling with neon effects
        style.configure('Epic.TButton',
                       background=self.colors['neon_cyan'],
                       foreground=self.colors['bg_primary'],
                       borderwidth=3,
                       relief='raised',
                       focuscolor='none',
                       font=self.fonts['header'],
                       padding=[15, 8])

        style.map('Epic.TButton',
                 background=[('active', self.colors['neon_pink']),
                           ('pressed', self.colors['neon_purple'])],
                 relief=[('pressed', 'sunken')])

        style.configure('EpicSuccess.TButton',
                       background=self.colors['neon_green'],
                       foreground=self.colors['bg_primary'],
                       borderwidth=3,
                       relief='raised')

        style.configure('EpicDanger.TButton',
                       background=self.colors['neon_red'],
                       foreground=self.colors['text_primary'],
                       borderwidth=3,
                       relief='raised')

        style.configure('EpicWarning.TButton',
                       background=self.colors['neon_yellow'],
                       foreground=self.colors['bg_primary'],
                       borderwidth=3,
                       relief='raised')

        # Epic Entry styling
        style.configure('Epic.TEntry',
                       fieldbackground=self.colors['bg_accent'],
                       background=self.colors['bg_accent'],
                       foreground=self.colors['text_primary'],
                       borderwidth=1,
                       insertcolor=self.colors['accent_primary'])

        # Epic Checkbutton styling
        style.configure('Epic.TCheckbutton',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       focuscolor='none',
                       font=self.fonts['normal'])

        style.map('Epic.TCheckbutton',
                 background=[('active', self.colors['bg_accent'])])

        # Epic LabelFrame styling
        style.configure('Epic.TLabelframe',
                       background=self.colors['bg_secondary'],
                       borderwidth=2,
                       relief='groove')

        style.configure('Epic.TLabelframe.Label',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_primary'],
                       font=self.fonts['header'])

    def create_epic_header(self):
        """Create INSANE cyberpunk header with matrix effects"""
        header_frame = tk.Frame(self.main_container, bg=self.colors['bg_primary'], height=150)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)

        # INSANE CYBERPUNK TITLE with multiple effects
        title_container = tk.Frame(header_frame, bg=self.colors['bg_primary'])
        title_container.pack(expand=True)

        # Matrix-style background text
        self.matrix_bg = tk.Label(title_container,
                                 text="01001000 01000001 01000011 01001011 01000101 01010010",
                                 bg=self.colors['bg_primary'],
                                 fg=self.colors['bg_accent'],
                                 font=self.fonts['matrix'])
        self.matrix_bg.pack(pady=(5, 0))

        # Main INSANE title
        self.main_title = tk.Label(title_container,
                                  text="💀 TANTRA DEATH BOT 💀",
                                  bg=self.colors['bg_primary'],
                                  fg=self.colors['neon_cyan'],
                                  font=self.fonts['mega_title'])
        self.main_title.pack(pady=(5, 0))

        # Subtitle with effects
        self.subtitle = tk.Label(title_container,
                                text="🔥 ULTIMATE DESTRUCTION SYSTEM 🔥",
                                bg=self.colors['bg_primary'],
                                fg=self.colors['neon_orange'],
                                font=self.fonts['header'])
        self.subtitle.pack()

        # Version with cyberpunk styling
        version_text = f">>> VERSION {self.version} | NEURAL NETWORK ACTIVATED <<<"
        self.version_label = tk.Label(title_container,
                                     text=version_text,
                                     bg=self.colors['bg_primary'],
                                     fg=self.colors['neon_green'],
                                     font=self.fonts['cyber_header'])
        self.version_label.pack(pady=(5, 0))

        # INSANE animated status with multiple indicators
        status_frame = tk.Frame(title_container, bg=self.colors['bg_primary'])
        status_frame.pack(pady=(10, 0))

        self.status_indicator1 = tk.Label(status_frame,
                                         text="◉ SYSTEM",
                                         bg=self.colors['bg_primary'],
                                         fg=self.colors['neon_green'],
                                         font=self.fonts['header'])
        self.status_indicator1.pack(side='left', padx=10)

        self.status_indicator2 = tk.Label(status_frame,
                                         text="◉ NEURAL",
                                         bg=self.colors['bg_primary'],
                                         fg=self.colors['neon_cyan'],
                                         font=self.fonts['header'])
        self.status_indicator2.pack(side='left', padx=10)

        self.status_indicator3 = tk.Label(status_frame,
                                         text="◉ MATRIX",
                                         bg=self.colors['bg_primary'],
                                         fg=self.colors['neon_pink'],
                                         font=self.fonts['header'])
        self.status_indicator3.pack(side='left', padx=10)

        # Start INSANE animations
        self.animate_insane_header()
        self.animate_matrix_background()
        self.animate_status_indicator()

    def animate_insane_header(self):
        """INSANE header animation with color cycling"""
        try:
            if hasattr(self, 'main_title'):
                colors = [self.colors['neon_cyan'], self.colors['neon_pink'],
                         self.colors['neon_green'], self.colors['neon_orange'],
                         self.colors['neon_purple'], self.colors['neon_yellow']]

                current_color = self.main_title.cget('fg')
                current_index = colors.index(current_color) if current_color in colors else 0
                next_index = (current_index + 1) % len(colors)

                self.main_title.config(fg=colors[next_index])

                # Also animate subtitle
                subtitle_colors = [self.colors['neon_orange'], self.colors['neon_red'],
                                 self.colors['neon_yellow'], self.colors['neon_pink']]
                subtitle_index = (next_index) % len(subtitle_colors)
                self.subtitle.config(fg=subtitle_colors[subtitle_index])

                self.root.after(300, self.animate_insane_header)
        except:
            pass

    def animate_matrix_background(self):
        """Animate matrix background text"""
        try:
            if hasattr(self, 'matrix_bg'):
                import random
                matrix_chars = ['01001000', '01000001', '01000011', '01001011',
                              '01000101', '01010010', '01001001', '01001110']
                random.shuffle(matrix_chars)
                matrix_text = ' '.join(matrix_chars[:6])
                self.matrix_bg.config(text=matrix_text)

                # Cycle through green shades
                green_shades = ['#003300', '#004400', '#005500', '#006600']
                current_color = self.matrix_bg.cget('fg')
                if current_color in green_shades:
                    next_index = (green_shades.index(current_color) + 1) % len(green_shades)
                else:
                    next_index = 0
                self.matrix_bg.config(fg=green_shades[next_index])

                self.root.after(200, self.animate_matrix_background)
        except:
            pass

    def animate_status_indicator(self):
        """INSANE status indicator animation"""
        try:
            if hasattr(self, 'status_indicator1'):
                # Cycle through different colors for each indicator
                colors1 = [self.colors['neon_green'], self.colors['neon_cyan'], self.colors['neon_blue']]
                colors2 = [self.colors['neon_cyan'], self.colors['neon_purple'], self.colors['neon_pink']]
                colors3 = [self.colors['neon_pink'], self.colors['neon_orange'], self.colors['neon_red']]

                import random
                self.status_indicator1.config(fg=random.choice(colors1))
                self.status_indicator2.config(fg=random.choice(colors2))
                self.status_indicator3.config(fg=random.choice(colors3))

                # Change text occasionally
                if random.randint(1, 10) == 1:
                    texts1 = ["◉ SYSTEM", "◉ ONLINE", "◉ ACTIVE", "◉ READY"]
                    texts2 = ["◉ NEURAL", "◉ BRAIN", "◉ AI", "◉ CORE"]
                    texts3 = ["◉ MATRIX", "◉ HACK", "◉ CODE", "◉ CYBER"]

                    self.status_indicator1.config(text=random.choice(texts1))
                    self.status_indicator2.config(text=random.choice(texts2))
                    self.status_indicator3.config(text=random.choice(texts3))

                self.root.after(150, self.animate_status_indicator)
        except:
            pass
        
    def setup_gui(self):
        """Initialize the epic gaming GUI"""
        self.root = tk.Tk()
        self.root.title(f"🔥 TANTRA BOT SYSTEM v{self.version} 🔥")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        self.root.minsize(900, 650)

        # INSANE CYBERPUNK THEME COLORS 🔥
        self.colors = {
            'bg_primary': '#000000',       # Pure black matrix
            'bg_secondary': '#0d1117',     # Dark matrix
            'bg_accent': '#161b22',        # Accent matrix
            'bg_glow': '#1a1f26',          # Glow background
            'neon_cyan': '#00ffff',        # Cyberpunk cyan
            'neon_pink': '#ff00ff',        # Hot pink neon
            'neon_green': '#00ff00',       # Matrix green
            'neon_orange': '#ff4500',      # Blazing orange
            'neon_purple': '#8a2be2',      # Electric purple
            'neon_yellow': '#ffff00',      # Lightning yellow
            'neon_red': '#ff0040',         # Danger red
            'neon_blue': '#0080ff',        # Electric blue
            'text_primary': '#ffffff',     # Pure white
            'text_glow': '#00ffff',        # Glowing cyan text
            'text_matrix': '#00ff00',      # Matrix green text
            'shadow_glow': '#00ffff40',    # Cyan glow shadow
            'matrix_glow': '#00ff0040',    # Green glow shadow
            'danger_glow': '#ff004040',    # Red glow shadow
            'warning_glow': '#ffff0040',   # Yellow glow shadow
        }

        # Configure root window
        self.root.configure(bg=self.colors['bg_primary'])

        # INSANE CYBERPUNK FONTS 💀
        self.fonts = {
            'title': font.Font(family="Impact", size=24, weight="bold"),
            'mega_title': font.Font(family="Impact", size=32, weight="bold"),
            'header': font.Font(family="Arial Black", size=14, weight="bold"),
            'cyber_header': font.Font(family="Courier New", size=12, weight="bold"),
            'normal': font.Font(family="Arial", size=11, weight="bold"),
            'small': font.Font(family="Arial", size=9),
            'mono': font.Font(family="Courier New", size=10, weight="bold"),
            'matrix': font.Font(family="Consolas", size=8, weight="bold")
        }

        # Setup epic styling
        self.setup_epic_style()

        # Create main container with gradient effect
        self.main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        self.main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # Create epic header
        self.create_epic_header()

        # Create notebook for tabs with custom styling
        self.notebook = ttk.Notebook(self.main_container, style='Epic.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Create INSANE cyberpunk tabs
        self.create_insane_main_tab()
        self.create_insane_skills_tab()
        self.create_insane_buffs_tab()
        self.create_insane_healing_tab()
        self.create_insane_login_tab()
        self.create_insane_settings_tab()
        self.create_insane_advanced_tab()
        self.create_insane_tools_tab()
        
        # Epic Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("🚀 READY - Press F8 to start bot")
        self.status_bar = tk.Label(self.main_container,
                                 textvariable=self.status_var,
                                 bg=self.colors['bg_accent'],
                                 fg=self.colors['text_primary'],
                                 font=self.fonts['normal'],
                                 relief=tk.RAISED,
                                 borderwidth=2,
                                 anchor=tk.W,
                                 padx=10,
                                 pady=5)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        # Epic Control buttons
        self.create_epic_control_buttons()
        
    def create_insane_main_tab(self):
        """Create INSANE cyberpunk main control tab"""
        main_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(main_frame, text="💀 DEATH CONTROL 💀")

        # Create scrollable frame
        canvas = tk.Canvas(main_frame, bg=self.colors['bg_secondary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Epic.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Game Connection Section
        connection_frame = ttk.LabelFrame(scrollable_frame, text="🔗 GAME CONNECTION", style='Epic.TLabelframe')
        connection_frame.pack(fill='x', padx=10, pady=10)

        ttk.Label(connection_frame, text="Game Window Title:", style='Epic.TLabel').grid(row=0, column=0, sticky='w', padx=10, pady=8)
        self.window_title_var = tk.StringVar(value=self.window_title)
        title_entry = ttk.Entry(connection_frame, textvariable=self.window_title_var, width=35, style='Epic.TEntry')
        title_entry.grid(row=0, column=1, padx=10, pady=8, sticky='ew')

        # Connection status
        self.connection_status = ttk.Label(connection_frame, text="● Disconnected",
                                         style='Epic.TLabel', foreground=self.colors['danger'])
        self.connection_status.grid(row=0, column=2, padx=10, pady=8)
        
        # Epic Monster Management Section
        monster_frame = ttk.LabelFrame(scrollable_frame, text="🎯 TARGET MONSTERS", style='Epic.TLabelframe')
        monster_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Monster list with epic styling
        monster_list_frame = tk.Frame(monster_frame, bg=self.colors['bg_secondary'])
        monster_list_frame.pack(fill='both', expand=True, padx=10, pady=10)

        ttk.Label(monster_list_frame, text="Priority Target List:", style='EpicHeader.TLabel').pack(anchor='w', pady=(0, 5))

        # Create epic listbox with scrollbar
        listbox_frame = tk.Frame(monster_list_frame, bg=self.colors['bg_secondary'])
        listbox_frame.pack(fill='both', expand=True)

        self.monster_listbox = tk.Listbox(listbox_frame,
                                         height=8,
                                         bg=self.colors['bg_accent'],
                                         fg=self.colors['text_primary'],
                                         selectbackground=self.colors['accent_primary'],
                                         selectforeground=self.colors['text_primary'],
                                         font=self.fonts['mono'],
                                         borderwidth=0,
                                         highlightthickness=1,
                                         highlightcolor=self.colors['accent_primary'])

        monster_scrollbar = tk.Scrollbar(listbox_frame, bg=self.colors['bg_accent'])
        monster_scrollbar.pack(side='right', fill='y')
        self.monster_listbox.pack(side='left', fill='both', expand=True)
        self.monster_listbox.config(yscrollcommand=monster_scrollbar.set)
        monster_scrollbar.config(command=self.monster_listbox.yview)

        # Epic button layout
        btn_frame1 = tk.Frame(monster_list_frame, bg=self.colors['bg_secondary'])
        btn_frame1.pack(fill='x', pady=(10, 5))

        ttk.Button(btn_frame1, text="➕ ADD", command=self.add_monster, style='Epic.TButton').pack(side='left', padx=5)
        ttk.Button(btn_frame1, text="❌ REMOVE", command=self.remove_monster, style='EpicDanger.TButton').pack(side='left', padx=5)
        ttk.Button(btn_frame1, text="🗑️ CLEAR ALL", command=self.clear_monsters, style='EpicWarning.TButton').pack(side='left', padx=5)

        btn_frame2 = tk.Frame(monster_list_frame, bg=self.colors['bg_secondary'])
        btn_frame2.pack(fill='x', pady=5)

        ttk.Button(btn_frame2, text="⬆️ MOVE UP", command=self.move_monster_up, style='Epic.TButton').pack(side='left', padx=5)
        ttk.Button(btn_frame2, text="⬇️ MOVE DOWN", command=self.move_monster_down, style='Epic.TButton').pack(side='left', padx=5)
        ttk.Button(btn_frame2, text="📥 IMPORT", command=self.import_monster_list, style='EpicSuccess.TButton').pack(side='left', padx=5)
        ttk.Button(btn_frame2, text="📤 EXPORT", command=self.export_monster_list, style='EpicSuccess.TButton').pack(side='left', padx=5)
        
        # Epic Auto Features Section
        auto_frame = ttk.LabelFrame(scrollable_frame, text="🤖 AUTO FEATURES", style='Epic.TLabelframe')
        auto_frame.pack(fill='x', padx=10, pady=10)

        auto_inner = tk.Frame(auto_frame, bg=self.colors['bg_secondary'])
        auto_inner.pack(fill='x', padx=10, pady=10)

        # Create grid layout for auto features
        self.auto_login_var = tk.BooleanVar(value=self.config['auto_features']['auto_login'])
        auto_login_cb = ttk.Checkbutton(auto_inner, text="🔐 Auto Login", variable=self.auto_login_var, style='Epic.TCheckbutton')
        auto_login_cb.grid(row=0, column=0, sticky='w', padx=10, pady=5)

        self.auto_repair_var = tk.BooleanVar(value=self.config['auto_features']['auto_repair'])
        auto_repair_cb = ttk.Checkbutton(auto_inner, text="🔧 Auto Repair", variable=self.auto_repair_var, style='Epic.TCheckbutton')
        auto_repair_cb.grid(row=0, column=1, sticky='w', padx=10, pady=5)

        self.auto_silf_var = tk.BooleanVar(value=self.config['auto_features']['auto_silf'])
        auto_silf_cb = ttk.Checkbutton(auto_inner, text="⚰️ Auto Silfrijan (Self Resurrect)", variable=self.auto_silf_var, style='Epic.TCheckbutton')
        auto_silf_cb.grid(row=1, column=0, columnspan=2, sticky='w', padx=10, pady=5)

        self.anti_stuck_var = tk.BooleanVar(value=self.config['auto_features']['anti_stuck'])
        anti_stuck_cb = ttk.Checkbutton(auto_inner, text="🌀 Anti-Stuck (Smart Movement)", variable=self.anti_stuck_var, style='Epic.TCheckbutton')
        anti_stuck_cb.grid(row=2, column=0, columnspan=2, sticky='w', padx=10, pady=5)
        
        # Load initial monster list
        for monster in self.config['monster_list']:
            self.monster_listbox.insert(tk.END, monster)

        # Epic Targeting Options Section
        targeting_frame = ttk.LabelFrame(scrollable_frame, text="🎯 TARGETING OPTIONS", style='Epic.TLabelframe')
        targeting_frame.pack(fill='x', padx=10, pady=10)

        targeting_inner = tk.Frame(targeting_frame, bg=self.colors['bg_secondary'])
        targeting_inner.pack(fill='x', padx=10, pady=10)

        self.priority_targeting_var = tk.BooleanVar(value=True)
        priority_cb = ttk.Checkbutton(targeting_inner, text="🎯 Priority Targeting (target monsters in list order)",
                       variable=self.priority_targeting_var, style='Epic.TCheckbutton')
        priority_cb.grid(row=0, column=0, columnspan=3, sticky='w', padx=5, pady=5)

        self.target_nearest_var = tk.BooleanVar(value=False)
        nearest_cb = ttk.Checkbutton(targeting_inner, text="📍 Target Nearest (use Tab key)",
                       variable=self.target_nearest_var, style='Epic.TCheckbutton')
        nearest_cb.grid(row=1, column=0, columnspan=3, sticky='w', padx=5, pady=5)

        self.auto_retarget_var = tk.BooleanVar(value=True)
        retarget_cb = ttk.Checkbutton(targeting_inner, text="🔄 Auto Re-target when target dies",
                       variable=self.auto_retarget_var, style='Epic.TCheckbutton')
        retarget_cb.grid(row=2, column=0, columnspan=3, sticky='w', padx=5, pady=5)

        # Target delay with epic styling
        delay_frame = tk.Frame(targeting_inner, bg=self.colors['bg_secondary'])
        delay_frame.grid(row=3, column=0, columnspan=3, sticky='ew', padx=5, pady=10)

        ttk.Label(delay_frame, text="⏱️ Target Delay:", style='Epic.TLabel').pack(side='left', padx=(0, 10))
        self.target_delay_var = tk.StringVar(value="1000")
        delay_entry = ttk.Entry(delay_frame, textvariable=self.target_delay_var, width=10, style='Epic.TEntry')
        delay_entry.pack(side='left', padx=(0, 5))
        ttk.Label(delay_frame, text="ms", style='Epic.TLabel').pack(side='left')
            
    def create_skills_tab(self):
        """Create the skills configuration tab"""
        skills_frame = ttk.Frame(self.notebook)
        self.notebook.add(skills_frame, text="Skills")
        
        # Skills configuration
        ttk.Label(skills_frame, text="Skill", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(skills_frame, text="Enabled", font=('Arial', 10, 'bold')).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(skills_frame, text="Delay (ms)", font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=5, pady=5)
        
        # Skill 1-4
        self.skill_vars = []
        self.skill_delay_vars = []
        
        for i in range(4):
            skill_num = i + 1
            ttk.Label(skills_frame, text=f"Skill {skill_num} (Key {skill_num})").grid(row=skill_num, column=0, sticky='w', padx=5, pady=2)
            
            var = tk.BooleanVar(value=self.config['skills'][f'skill{skill_num}_enabled'])
            self.skill_vars.append(var)
            ttk.Checkbutton(skills_frame, variable=var).grid(row=skill_num, column=1, padx=5, pady=2)
            
            delay_var = tk.StringVar(value=str(self.config['skills'][f'skill{skill_num}_delay']))
            self.skill_delay_vars.append(delay_var)
            ttk.Entry(skills_frame, textvariable=delay_var, width=10).grid(row=skill_num, column=2, padx=5, pady=2)
        
        # Additional skill options
        ttk.Separator(skills_frame, orient='horizontal').grid(row=5, column=0, columnspan=3, sticky='ew', pady=10)
        
        self.use_r_var = tk.BooleanVar(value=self.config['skills']['use_r_key'])
        ttk.Checkbutton(skills_frame, text="Use R key (uncheck for Mage)", variable=self.use_r_var).grid(row=6, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        
        self.loot_var = tk.BooleanVar(value=self.config['skills']['loot_enabled'])
        ttk.Checkbutton(skills_frame, text="Auto Loot (F key)", variable=self.loot_var).grid(row=7, column=0, columnspan=2, sticky='w', padx=5, pady=2)
        
        self.loot_delay_var = tk.StringVar(value=str(self.config['skills']['loot_delay']))
        ttk.Label(skills_frame, text="Loot Delay (ms):").grid(row=8, column=0, sticky='w', padx=5, pady=2)
        ttk.Entry(skills_frame, textvariable=self.loot_delay_var, width=10).grid(row=8, column=1, padx=5, pady=2)
        
    def create_buffs_tab(self):
        """Create the buffs configuration tab"""
        buffs_frame = ttk.Frame(self.notebook)
        self.notebook.add(buffs_frame, text="Buffs")
        
        ttk.Label(buffs_frame, text="Buff", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(buffs_frame, text="Enabled", font=('Arial', 10, 'bold')).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(buffs_frame, text="Duration (ms)", font=('Arial', 10, 'bold')).grid(row=0, column=2, padx=5, pady=5)
        
        self.buff_vars = []
        self.buff_duration_vars = []
        
        for i in range(4):
            buff_num = i + 1
            ttk.Label(buffs_frame, text=f"Buff {buff_num} (F2+{buff_num})").grid(row=buff_num, column=0, sticky='w', padx=5, pady=2)
            
            var = tk.BooleanVar(value=self.config['buffs'][f'buff{buff_num}_enabled'])
            self.buff_vars.append(var)
            ttk.Checkbutton(buffs_frame, variable=var).grid(row=buff_num, column=1, padx=5, pady=2)
            
            duration_var = tk.StringVar(value=str(self.config['buffs'][f'buff{buff_num}_duration']))
            self.buff_duration_vars.append(duration_var)
            ttk.Entry(buffs_frame, textvariable=duration_var, width=10).grid(row=buff_num, column=2, padx=5, pady=2)
            
        ttk.Label(buffs_frame, text="Note: Buffs are cast using F2 function keys", 
                 font=('Arial', 9, 'italic')).grid(row=5, column=0, columnspan=3, pady=10)
                 
    def create_healing_tab(self):
        """Create the healing configuration tab"""
        healing_frame = ttk.Frame(self.notebook)
        self.notebook.add(healing_frame, text="Healing")
        
        # HP Healing
        hp_frame = ttk.LabelFrame(healing_frame, text="HP Healing")
        hp_frame.pack(fill='x', padx=5, pady=5)
        
        self.heal_skill_var = tk.BooleanVar(value=self.config['healing']['heal_skill_enabled'])
        ttk.Checkbutton(hp_frame, text="Use Heal Skill (Key 7)", variable=self.heal_skill_var).grid(row=0, column=0, sticky='w', padx=5, pady=2)
        
        ttk.Label(hp_frame, text="Heal at HP %:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.heal_percentage_var = tk.StringVar(value=str(self.config['healing']['heal_percentage']))
        ttk.Entry(hp_frame, textvariable=self.heal_percentage_var, width=10).grid(row=1, column=1, padx=5, pady=2)
        
        # HP Potions
        self.potion_var = tk.BooleanVar(value=self.config['healing']['potion_enabled'])
        ttk.Checkbutton(hp_frame, text="Use HP Potions (Key 8)", variable=self.potion_var).grid(row=2, column=0, sticky='w', padx=5, pady=2)
        
        ttk.Label(hp_frame, text="Potion at HP %:").grid(row=3, column=0, sticky='w', padx=5, pady=2)
        self.potion_percentage_var = tk.StringVar(value=str(self.config['healing']['potion_percentage']))
        ttk.Entry(hp_frame, textvariable=self.potion_percentage_var, width=10).grid(row=3, column=1, padx=5, pady=2)
        
        # TP Potions
        tp_frame = ttk.LabelFrame(healing_frame, text="TP (Mana) Management")
        tp_frame.pack(fill='x', padx=5, pady=5)
        
        self.tp_potion_var = tk.BooleanVar(value=self.config['healing']['tp_potion_enabled'])
        ttk.Checkbutton(tp_frame, text="Use TP Potions (Key 9)", variable=self.tp_potion_var).grid(row=0, column=0, sticky='w', padx=5, pady=2)
        
        ttk.Label(tp_frame, text="TP Potion at %:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.tp_percentage_var = tk.StringVar(value=str(self.config['healing']['tp_percentage']))
        ttk.Entry(tp_frame, textvariable=self.tp_percentage_var, width=10).grid(row=1, column=1, padx=5, pady=2)

    def create_login_tab(self):
        """Create the auto-login configuration tab"""
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="Auto Login")

        # Login credentials
        cred_frame = ttk.LabelFrame(login_frame, text="Login Credentials")
        cred_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(cred_frame, text="Username:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.username_var = tk.StringVar(value=self.config['login']['username'])
        ttk.Entry(cred_frame, textvariable=self.username_var, width=20).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(cred_frame, text="Password:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.password_var = tk.StringVar(value=self.config['login']['password'])
        ttk.Entry(cred_frame, textvariable=self.password_var, width=20, show='*').grid(row=1, column=1, padx=5, pady=2)

        ttk.Label(cred_frame, text="Tantra Directory:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        self.tantra_dir_var = tk.StringVar(value=self.config['login']['tantra_dir'])
        dir_frame = ttk.Frame(cred_frame)
        dir_frame.grid(row=2, column=1, sticky='ew', padx=5, pady=2)
        ttk.Entry(dir_frame, textvariable=self.tantra_dir_var, width=15).pack(side='left')
        ttk.Button(dir_frame, text="Browse", command=self.browse_tantra_dir).pack(side='left', padx=(5,0))

        # Selection options
        select_frame = ttk.LabelFrame(login_frame, text="Game Selection")
        select_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(select_frame, text="Server:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.server_var = tk.StringVar(value=str(self.config['login']['server_selection']))
        ttk.Entry(select_frame, textvariable=self.server_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(select_frame, text="Character:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.character_var = tk.StringVar(value=str(self.config['login']['character_selection']))
        ttk.Entry(select_frame, textvariable=self.character_var, width=10).grid(row=1, column=1, padx=5, pady=2)

        # Timing delays
        delay_frame = ttk.LabelFrame(login_frame, text="Login Delays (milliseconds)")
        delay_frame.pack(fill='x', padx=5, pady=5)

        delays = [
            ("Wait for Tantra:", 'wait_for_tantra'),
            ("Wait for Login:", 'wait_for_login'),
            ("Server Select:", 'server_select'),
            ("Character Select:", 'character_select'),
            ("Enter Game:", 'enter_game')
        ]

        self.delay_vars = {}
        for i, (label, key) in enumerate(delays):
            ttk.Label(delay_frame, text=label).grid(row=i, column=0, sticky='w', padx=5, pady=2)
            var = tk.StringVar(value=str(self.config['login']['delays'][key]))
            self.delay_vars[key] = var
            ttk.Entry(delay_frame, textvariable=var, width=10).grid(row=i, column=1, padx=5, pady=2)

        # Login buttons
        btn_frame = ttk.Frame(login_frame)
        btn_frame.pack(fill='x', padx=5, pady=10)
        ttk.Button(btn_frame, text="Login Once", command=self.login_once).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Test Connection", command=self.test_connection).pack(side='left', padx=5)

    def create_settings_tab(self):
        """Create the settings and advanced options tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Settings")

        # Party settings
        party_frame = ttk.LabelFrame(settings_frame, text="Party Settings")
        party_frame.pack(fill='x', padx=5, pady=5)

        self.party_accept_var = tk.BooleanVar(value=self.config['party']['auto_accept'])
        ttk.Checkbutton(party_frame, text="Auto Accept Party Invites", variable=self.party_accept_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(party_frame, text="Join Reply:").pack(anchor='w', padx=5)
        self.party_join_var = tk.StringVar(value=self.config['party']['join_reply'])
        ttk.Entry(party_frame, textvariable=self.party_join_var, width=40).pack(anchor='w', padx=5, pady=2)

        ttk.Label(party_frame, text="Deny Reply:").pack(anchor='w', padx=5)
        self.party_deny_var = tk.StringVar(value=self.config['party']['deny_reply'])
        ttk.Entry(party_frame, textvariable=self.party_deny_var, width=40).pack(anchor='w', padx=5, pady=2)

        # Trade settings
        trade_frame = ttk.LabelFrame(settings_frame, text="Trade Settings")
        trade_frame.pack(fill='x', padx=5, pady=5)

        self.trade_deny_var = tk.BooleanVar(value=self.config['trade']['auto_deny'])
        ttk.Checkbutton(trade_frame, text="Auto Deny Trade Requests", variable=self.trade_deny_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(trade_frame, text="Deny Reply:").pack(anchor='w', padx=5)
        self.trade_reply_var = tk.StringVar(value=self.config['trade']['deny_reply'])
        ttk.Entry(trade_frame, textvariable=self.trade_reply_var, width=40).pack(anchor='w', padx=5, pady=2)

        # PM Reply settings
        pm_frame = ttk.LabelFrame(settings_frame, text="Private Message Auto-Reply")
        pm_frame.pack(fill='x', padx=5, pady=5)

        self.pm_reply_enabled_var = tk.BooleanVar(value=self.config['pm_reply']['enabled'])
        ttk.Checkbutton(pm_frame, text="Enable Auto-Reply to PMs", variable=self.pm_reply_enabled_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(pm_frame, text="Reply Text:").pack(anchor='w', padx=5)
        self.pm_reply_text_var = tk.StringVar(value=self.config['pm_reply']['reply_text'])
        ttk.Entry(pm_frame, textvariable=self.pm_reply_text_var, width=40).pack(anchor='w', padx=5, pady=2)

        # Anti-stuck settings
        stuck_frame = ttk.LabelFrame(settings_frame, text="Anti-Stuck Settings")
        stuck_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(stuck_frame, text="Spin Time (ms):").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.stuck_spin_var = tk.StringVar(value=str(self.config['auto_features']['stuck_spin_time']))
        ttk.Entry(stuck_frame, textvariable=self.stuck_spin_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(stuck_frame, text="Move Time (ms):").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.stuck_move_var = tk.StringVar(value=str(self.config['auto_features']['stuck_move_time']))
        ttk.Entry(stuck_frame, textvariable=self.stuck_move_var, width=10).grid(row=1, column=1, padx=5, pady=2)

        # Silfrijan coordinates
        silf_frame = ttk.LabelFrame(settings_frame, text="Silfrijan Coordinates")
        silf_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(silf_frame, text="X:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.silf_x_var = tk.StringVar(value=str(self.config['auto_features']['silf_x']))
        ttk.Entry(silf_frame, textvariable=self.silf_x_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(silf_frame, text="Y:").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        self.silf_y_var = tk.StringVar(value=str(self.config['auto_features']['silf_y']))
        ttk.Entry(silf_frame, textvariable=self.silf_y_var, width=10).grid(row=0, column=3, padx=5, pady=2)

        ttk.Button(silf_frame, text="Get Current Position (F5)", command=self.get_mouse_position).grid(row=1, column=0, columnspan=4, pady=5)

    def create_advanced_tab(self):
        """Create the advanced features tab"""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="Advanced")

        # Connection monitoring
        conn_frame = ttk.LabelFrame(advanced_frame, text="Connection Monitoring")
        conn_frame.pack(fill='x', padx=5, pady=5)

        self.connection_monitor_var = tk.BooleanVar(value=self.config['tools']['connection_monitor'])
        ttk.Checkbutton(conn_frame, text="Monitor Connection", variable=self.connection_monitor_var).pack(anchor='w', padx=5, pady=2)

        self.auto_reconnect_var = tk.BooleanVar(value=self.config['advanced']['auto_reconnect'])
        ttk.Checkbutton(conn_frame, text="Auto Reconnect", variable=self.auto_reconnect_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(conn_frame, text="Reconnect Delay (ms):").pack(anchor='w', padx=5)
        self.reconnect_delay_var = tk.StringVar(value=str(self.config['advanced']['reconnect_delay']))
        ttk.Entry(conn_frame, textvariable=self.reconnect_delay_var, width=10).pack(anchor='w', padx=5, pady=2)

        # Advanced anti-stuck
        stuck_frame = ttk.LabelFrame(advanced_frame, text="Advanced Anti-Stuck")
        stuck_frame.pack(fill='x', padx=5, pady=5)

        self.advanced_anti_stuck_var = tk.BooleanVar(value=self.config['advanced']['advanced_anti_stuck'])
        ttk.Checkbutton(stuck_frame, text="Enable Advanced Anti-Stuck", variable=self.advanced_anti_stuck_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(stuck_frame, text="Stuck Detection Threshold (ms):").pack(anchor='w', padx=5)
        self.stuck_threshold_var = tk.StringVar(value=str(self.config['advanced']['stuck_detection_threshold']))
        ttk.Entry(stuck_frame, textvariable=self.stuck_threshold_var, width=10).pack(anchor='w', padx=5, pady=2)

        # Equipment monitoring
        equip_frame = ttk.LabelFrame(advanced_frame, text="Equipment Monitoring")
        equip_frame.pack(fill='x', padx=5, pady=5)

        ttk.Label(equip_frame, text="Repair Threshold (%):").pack(anchor='w', padx=5)
        self.repair_threshold_var = tk.StringVar(value=str(self.config['advanced']['repair_threshold']))
        ttk.Entry(equip_frame, textvariable=self.repair_threshold_var, width=10).pack(anchor='w', padx=5, pady=2)

        # Status monitoring
        status_frame = ttk.LabelFrame(advanced_frame, text="Status Monitoring")
        status_frame.pack(fill='x', padx=5, pady=5)

        self.status_monitoring_var = tk.BooleanVar(value=self.config['advanced']['status_monitoring'])
        ttk.Checkbutton(status_frame, text="Enable Status Monitoring", variable=self.status_monitoring_var).pack(anchor='w', padx=5, pady=2)

        # Character stats display
        self.stats_text = tk.Text(status_frame, height=6, width=50)
        self.stats_text.pack(padx=5, pady=5)
        self.stats_text.insert('1.0', "Character stats will appear here when bot is running...")
        self.stats_text.config(state='disabled')

        # Memory scanning
        memory_frame = ttk.LabelFrame(advanced_frame, text="Memory Scanner")
        memory_frame.pack(fill='x', padx=5, pady=5)

        self.memory_scan_var = tk.BooleanVar(value=self.config['advanced']['memory_scan_enabled'])
        ttk.Checkbutton(memory_frame, text="Enable Memory Scanning", variable=self.memory_scan_var).pack(anchor='w', padx=5, pady=2)

        ttk.Button(memory_frame, text="Scan for Character Base", command=self.scan_character_base).pack(side='left', padx=5, pady=5)
        ttk.Button(memory_frame, text="Scan for Target Base", command=self.scan_target_base).pack(side='left', padx=5, pady=5)

    def create_tools_tab(self):
        """Create the tools and utilities tab"""
        tools_frame = ttk.Frame(self.notebook)
        self.notebook.add(tools_frame, text="Tools")

        # Trade spam
        trade_frame = ttk.LabelFrame(tools_frame, text="Trade Spam (F9)")
        trade_frame.pack(fill='x', padx=5, pady=5)

        self.trade_spam_enabled_var = tk.BooleanVar(value=self.config['tools']['trade_spam_enabled'])
        ttk.Checkbutton(trade_frame, text="Enable Trade Spam", variable=self.trade_spam_enabled_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(trade_frame, text="Spam Message:").pack(anchor='w', padx=5)
        self.trade_spam_message_var = tk.StringVar(value=self.config['tools']['trade_spam_message'])
        ttk.Entry(trade_frame, textvariable=self.trade_spam_message_var, width=40).pack(anchor='w', padx=5, pady=2)

        ttk.Label(trade_frame, text="Spam Delay (ms):").pack(anchor='w', padx=5)
        self.trade_spam_delay_var = tk.StringVar(value=str(self.config['tools']['trade_spam_delay']))
        ttk.Entry(trade_frame, textvariable=self.trade_spam_delay_var, width=10).pack(anchor='w', padx=5, pady=2)

        trade_btn_frame = ttk.Frame(trade_frame)
        trade_btn_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(trade_btn_frame, text="Start Trade Spam", command=self.start_trade_spam).pack(side='left', padx=5)
        ttk.Button(trade_btn_frame, text="Stop Trade Spam", command=self.stop_trade_spam).pack(side='left', padx=5)

        # Left clicker
        clicker_frame = ttk.LabelFrame(tools_frame, text="Auto Clicker (F11)")
        clicker_frame.pack(fill='x', padx=5, pady=5)

        self.left_clicker_enabled_var = tk.BooleanVar(value=self.config['tools']['left_clicker_enabled'])
        ttk.Checkbutton(clicker_frame, text="Enable Auto Clicker", variable=self.left_clicker_enabled_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(clicker_frame, text="Click Delay (ms):").pack(anchor='w', padx=5)
        self.left_clicker_delay_var = tk.StringVar(value=str(self.config['tools']['left_clicker_delay']))
        ttk.Entry(clicker_frame, textvariable=self.left_clicker_delay_var, width=10).pack(anchor='w', padx=5, pady=2)

        clicker_btn_frame = ttk.Frame(clicker_frame)
        clicker_btn_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(clicker_btn_frame, text="Start Auto Clicker", command=self.start_left_clicker).pack(side='left', padx=5)
        ttk.Button(clicker_btn_frame, text="Stop Auto Clicker", command=self.stop_left_clicker).pack(side='left', padx=5)

        # Quiz auto-answer
        quiz_frame = ttk.LabelFrame(tools_frame, text="Quiz Auto-Answer")
        quiz_frame.pack(fill='x', padx=5, pady=5)

        self.auto_answer_quiz_var = tk.BooleanVar(value=self.config['tools']['auto_answer_quiz'])
        ttk.Checkbutton(quiz_frame, text="Enable Auto Quiz Answer", variable=self.auto_answer_quiz_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(quiz_frame, text="Quiz Answers File:").pack(anchor='w', padx=5)
        quiz_file_frame = ttk.Frame(quiz_frame)
        quiz_file_frame.pack(fill='x', padx=5, pady=2)

        self.quiz_file_var = tk.StringVar(value=self.config['tools']['quiz_answers_file'])
        ttk.Entry(quiz_file_frame, textvariable=self.quiz_file_var, width=30).pack(side='left')
        ttk.Button(quiz_file_frame, text="Browse", command=self.browse_quiz_file).pack(side='left', padx=(5,0))
        ttk.Button(quiz_file_frame, text="Create", command=self.create_quiz_file).pack(side='left', padx=(5,0))

        # Teleport hack
        teleport_frame = ttk.LabelFrame(tools_frame, text="Mandara Teleport Hack")
        teleport_frame.pack(fill='x', padx=5, pady=5)

        self.teleport_enabled_var = tk.BooleanVar(value=self.config['teleport']['enabled'])
        ttk.Checkbutton(teleport_frame, text="Enable Teleport Hack", variable=self.teleport_enabled_var).pack(anchor='w', padx=5, pady=2)

        ttk.Label(teleport_frame, text="Target Address (Hex):").pack(anchor='w', padx=5)
        self.teleport_address_var = tk.StringVar(value=hex(self.config['teleport']['target_address']))
        ttk.Entry(teleport_frame, textvariable=self.teleport_address_var, width=20).pack(anchor='w', padx=5, pady=2)

        teleport_btn_frame = ttk.Frame(teleport_frame)
        teleport_btn_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(teleport_btn_frame, text="Search Address", command=self.search_teleport_address).pack(side='left', padx=5)
        ttk.Button(teleport_btn_frame, text="Hack Portal", command=self.hack_portal).pack(side='left', padx=5)

    def create_epic_control_buttons(self):
        """Create epic control buttons with amazing styling"""
        control_frame = tk.Frame(self.main_container, bg=self.colors['bg_primary'], height=80)
        control_frame.pack(side=tk.BOTTOM, fill='x', padx=5, pady=10)
        control_frame.pack_propagate(False)

        # Main control buttons with epic styling
        main_controls = tk.Frame(control_frame, bg=self.colors['bg_primary'])
        main_controls.pack(side='left', fill='y')

        self.start_btn = tk.Button(main_controls,
                                  text="💀 ACTIVATE DEATH MODE 💀",
                                  command=self.start_bot,
                                  bg=self.colors['neon_green'],
                                  fg=self.colors['bg_primary'],
                                  font=self.fonts['header'],
                                  borderwidth=5,
                                  relief='raised',
                                  padx=25,
                                  pady=15,
                                  cursor='hand2')
        self.start_btn.pack(side='left', padx=10)

        self.pause_btn = tk.Button(main_controls,
                                  text="⚡ SUSPEND MATRIX ⚡",
                                  command=self.pause_bot,
                                  bg=self.colors['neon_yellow'],
                                  fg=self.colors['bg_primary'],
                                  font=self.fonts['header'],
                                  borderwidth=5,
                                  relief='raised',
                                  padx=25,
                                  pady=15,
                                  cursor='hand2',
                                  state='disabled')
        self.pause_btn.pack(side='left', padx=10)

        self.stop_btn = tk.Button(main_controls,
                                 text="🔥 TERMINATE SYSTEM 🔥",
                                 command=self.stop_bot,
                                 bg=self.colors['neon_red'],
                                 fg=self.colors['text_primary'],
                                 font=self.fonts['header'],
                                 borderwidth=5,
                                 relief='raised',
                                 padx=25,
                                 pady=15,
                                 cursor='hand2',
                                 state='disabled')
        self.stop_btn.pack(side='left', padx=10)

        # Config buttons
        config_controls = tk.Frame(control_frame, bg=self.colors['bg_primary'])
        config_controls.pack(side='right', fill='y')

        save_btn = tk.Button(config_controls,
                            text="💾 SAVE CONFIG",
                            command=self.save_config,
                            bg=self.colors['info'],
                            fg=self.colors['text_primary'],
                            font=self.fonts['normal'],
                            borderwidth=0,
                            padx=15,
                            pady=8,
                            cursor='hand2')
        save_btn.pack(side='right', padx=5)

        load_btn = tk.Button(config_controls,
                            text="📁 LOAD CONFIG",
                            command=self.load_config,
                            bg=self.colors['accent_secondary'],
                            fg=self.colors['text_primary'],
                            font=self.fonts['normal'],
                            borderwidth=0,
                            padx=15,
                            pady=8,
                            cursor='hand2')
        load_btn.pack(side='right', padx=5)

        # Epic pause checkbox
        self.pause_var = tk.BooleanVar()
        pause_frame = tk.Frame(config_controls, bg=self.colors['bg_primary'])
        pause_frame.pack(side='right', padx=20)

        pause_cb = tk.Checkbutton(pause_frame,
                                 text="⏸️ Quick Pause",
                                 variable=self.pause_var,
                                 command=self.toggle_pause,
                                 bg=self.colors['bg_primary'],
                                 fg=self.colors['text_secondary'],
                                 selectcolor=self.colors['bg_accent'],
                                 font=self.fonts['normal'],
                                 borderwidth=0,
                                 cursor='hand2')
        pause_cb.pack()

        # Add hover effects
        self.add_button_hover_effects()

    def add_button_hover_effects(self):
        """Add INSANE cyberpunk hover effects with neon glow"""
        def on_enter_insane(event, hover_color, hover_text=None):
            event.widget.config(bg=hover_color, relief='sunken', borderwidth=8)
            if hover_text:
                event.widget.config(text=hover_text)
            # Add pulsing effect
            self.pulse_button(event.widget, hover_color)

        def on_leave_insane(event, original_color, original_text=None):
            event.widget.config(bg=original_color, relief='raised', borderwidth=5)
            if original_text:
                event.widget.config(text=original_text)

        # INSANE hover effects for main buttons
        insane_buttons = [
            (self.start_btn, self.colors['neon_green'], self.colors['neon_cyan'],
             "💀 ACTIVATE DEATH MODE 💀", "🔥 UNLEASH HELL 🔥"),
            (self.pause_btn, self.colors['neon_yellow'], self.colors['neon_orange'],
             "⚡ SUSPEND MATRIX ⚡", "💥 FREEZE TIME 💥"),
            (self.stop_btn, self.colors['neon_red'], self.colors['neon_purple'],
             "🔥 TERMINATE SYSTEM 🔥", "💀 TOTAL SHUTDOWN 💀")
        ]

        for btn, original, hover, original_text, hover_text in insane_buttons:
            btn.bind("<Enter>", lambda e, h=hover, ht=hover_text: on_enter_insane(e, h, ht))
            btn.bind("<Leave>", lambda e, o=original, ot=original_text: on_leave_insane(e, o, ot))

    def pulse_button(self, button, color):
        """Create pulsing effect for buttons"""
        try:
            # Create pulsing by changing borderwidth
            current_border = button.cget('borderwidth')
            if current_border < 10:
                button.config(borderwidth=current_border + 1)
                self.root.after(50, lambda: self.pulse_button(button, color))
        except:
            pass

    def update_epic_status(self, message, status_type="info"):
        """Update status with epic styling and icons"""
        icons = {
            'info': '💡',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'running': '🔥',
            'paused': '⏸️',
            'stopped': '⏹️'
        }

        colors = {
            'info': self.colors['info'],
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'error': self.colors['danger'],
            'running': self.colors['accent_primary'],
            'paused': self.colors['warning'],
            'stopped': self.colors['text_secondary']
        }

        icon = icons.get(status_type, '💡')
        color = colors.get(status_type, self.colors['text_primary'])

        self.status_var.set(f"{icon} {message}")
        self.status_bar.config(fg=color)

        # Update status indicator
        if hasattr(self, 'status_indicator'):
            if status_type == 'running':
                self.status_indicator.config(text="● RUNNING", fg=self.colors['success'])
            elif status_type == 'paused':
                self.status_indicator.config(text="● PAUSED", fg=self.colors['warning'])
            elif status_type == 'stopped':
                self.status_indicator.config(text="● STOPPED", fg=self.colors['danger'])
            else:
                self.status_indicator.config(text="● READY", fg=self.colors['info'])

    def setup_hotkeys(self):
        """Setup global hotkeys"""
        try:
            keyboard.add_hotkey('f8', self.start_bot)
            keyboard.add_hotkey('f9', self.trade_spam)
            keyboard.add_hotkey('f10', self.pause_bot)
            keyboard.add_hotkey('f11', self.left_clicker)
            keyboard.add_hotkey('f5', self.get_mouse_position)
            keyboard.add_hotkey('shift+alt+m', self.show_main_menu)
            keyboard.add_hotkey('shift+alt+a', self.show_login_menu)
        except Exception as e:
            print(f"Warning: Could not set up hotkeys: {e}")

    # GUI Event Handlers
    def add_monster(self):
        """Add a monster to the target list"""
        monster_name = simpledialog.askstring("Add Monster", "Enter monster name:")
        if monster_name:
            self.monster_listbox.insert(tk.END, monster_name)
            self.config['monster_list'].append(monster_name)

    def remove_monster(self):
        """Remove selected monster from the list"""
        selection = self.monster_listbox.curselection()
        if selection:
            index = selection[0]
            self.monster_listbox.delete(index)
            if index < len(self.config['monster_list']):
                del self.config['monster_list'][index]

    def clear_monsters(self):
        """Clear all monsters from the list"""
        if messagebox.askyesno("Confirm", "Clear all monsters from the list?"):
            self.monster_listbox.delete(0, tk.END)
            self.config['monster_list'].clear()

    def move_monster_up(self):
        """Move selected monster up in priority"""
        selection = self.monster_listbox.curselection()
        if selection and selection[0] > 0:
            index = selection[0]
            # Swap in listbox
            monster = self.monster_listbox.get(index)
            self.monster_listbox.delete(index)
            self.monster_listbox.insert(index - 1, monster)
            self.monster_listbox.selection_set(index - 1)

            # Swap in config
            self.config['monster_list'][index], self.config['monster_list'][index - 1] = \
                self.config['monster_list'][index - 1], self.config['monster_list'][index]

    def move_monster_down(self):
        """Move selected monster down in priority"""
        selection = self.monster_listbox.curselection()
        if selection and selection[0] < self.monster_listbox.size() - 1:
            index = selection[0]
            # Swap in listbox
            monster = self.monster_listbox.get(index)
            self.monster_listbox.delete(index)
            self.monster_listbox.insert(index + 1, monster)
            self.monster_listbox.selection_set(index + 1)

            # Swap in config
            self.config['monster_list'][index], self.config['monster_list'][index + 1] = \
                self.config['monster_list'][index + 1], self.config['monster_list'][index]

    def import_monster_list(self):
        """Import monster list from file"""
        filename = filedialog.askopenfilename(
            title="Import Monster List",
            filetypes=[("Text files", "*.txt"), ("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    if filename.endswith('.json'):
                        data = json.load(f)
                        if isinstance(data, list):
                            monsters = data
                        elif isinstance(data, dict) and 'monster_list' in data:
                            monsters = data['monster_list']
                        else:
                            raise ValueError("Invalid JSON format")
                    else:
                        # Text file - one monster per line
                        monsters = [line.strip() for line in f.readlines() if line.strip()]

                # Add to current list
                for monster in monsters:
                    if monster not in self.config['monster_list']:
                        self.monster_listbox.insert(tk.END, monster)
                        self.config['monster_list'].append(monster)

                messagebox.showinfo("Success", f"Imported {len(monsters)} monsters!")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to import monster list: {e}")

    def export_monster_list(self):
        """Export monster list to file"""
        if not self.config['monster_list']:
            messagebox.showwarning("Warning", "No monsters to export!")
            return

        filename = filedialog.asksaveasfilename(
            title="Export Monster List",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    if filename.endswith('.json'):
                        json.dump(self.config['monster_list'], f, indent=2, ensure_ascii=False)
                    else:
                        # Text file - one monster per line
                        for monster in self.config['monster_list']:
                            f.write(f"{monster}\n")

                messagebox.showinfo("Success", f"Exported {len(self.config['monster_list'])} monsters!")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export monster list: {e}")

    def browse_tantra_dir(self):
        """Browse for Tantra installation directory"""
        directory = filedialog.askdirectory(title="Select Tantra Installation Directory")
        if directory:
            self.tantra_dir_var.set(directory)

    def get_mouse_position(self):
        """Get current mouse position for Silfrijan coordinates"""
        x, y = pyautogui.position()
        self.silf_x_var.set(str(x))
        self.silf_y_var.set(str(y))
        messagebox.showinfo("Position Set", f"Silfrijan position set to: {x}, {y}")

    def toggle_pause(self):
        """Toggle bot pause state"""
        self.paused = self.pause_var.get()
        if self.paused:
            self.status_var.set("Bot Paused")
        else:
            self.status_var.set("Bot Running")

    # Bot Control Methods
    def start_bot(self):
        """Start the epic bot"""
        if not self.running:
            self.update_config_from_gui()

            # Find game window
            if not self.find_game_window():
                self.update_epic_status("Could not find Tantra game window!", "error")
                messagebox.showerror("Connection Error", "Could not find Tantra game window!\nMake sure the game is running.")
                return

            self.running = True
            self.paused = False

            # Update epic button states
            self.start_btn.config(state='disabled', text="🔥 RUNNING...")
            self.pause_btn.config(state='normal')
            self.stop_btn.config(state='normal')

            self.update_epic_status("Bot started successfully! Automation active.", "running")

            # Start bot thread
            self.bot_thread = threading.Thread(target=self.bot_main_loop, daemon=True)
            self.bot_thread.start()

    def pause_bot(self):
        """Pause/unpause the epic bot"""
        if self.running:
            self.paused = not self.paused
            if self.paused:
                self.update_epic_status("Bot paused - automation suspended", "paused")
                self.pause_btn.config(text="▶️ RESUME (F10)")
            else:
                self.update_epic_status("Bot resumed - automation active", "running")
                self.pause_btn.config(text="⏸️ PAUSE (F10)")

    def stop_bot(self):
        """Stop the epic bot"""
        self.running = False
        self.paused = False

        # Update epic button states
        self.start_btn.config(state='normal', text="🚀 START BOT (F8)")
        self.pause_btn.config(state='disabled', text="⏸️ PAUSE (F10)")
        self.stop_btn.config(state='disabled')

        self.update_epic_status("Bot stopped - automation disabled", "stopped")

    def find_game_window(self):
        """Find the game window handle"""
        try:
            self.game_hwnd = win32gui.FindWindow(None, self.window_title_var.get())
            if self.game_hwnd:
                _, self.game_pid = win32process.GetWindowThreadProcessId(self.game_hwnd)
                return True
        except Exception as e:
            print(f"Error finding game window: {e}")
        return False

    def send_key_to_game(self, key, hold_time=0.1):
        """Send a key press to the game window"""
        if not self.game_hwnd:
            return

        try:
            # Bring window to foreground
            win32gui.SetForegroundWindow(self.game_hwnd)
            time.sleep(0.05)

            # Send key
            if isinstance(key, str) and len(key) == 1:
                vk_code = ord(key.upper())
            else:
                # Handle special keys
                key_map = {
                    'enter': 0x0D, 'tab': 0x09, 'space': 0x20,
                    'f1': 0x70, 'f2': 0x71, 'f8': 0x77, 'f10': 0x79,
                    'a': 0x41, 'w': 0x57, 'r': 0x52, 'f': 0x46,
                    '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
                    '7': 0x37, '8': 0x38, '9': 0x39
                }
                vk_code = key_map.get(key.lower(), 0x20)

            # Send key down
            win32api.PostMessage(self.game_hwnd, win32con.WM_KEYDOWN, vk_code, 0)
            time.sleep(hold_time)
            # Send key up
            win32api.PostMessage(self.game_hwnd, win32con.WM_KEYUP, vk_code, 0)

        except Exception as e:
            print(f"Error sending key {key}: {e}")

    def click_in_game(self, x, y):
        """Click at specific coordinates in the game window"""
        if not self.game_hwnd:
            return

        try:
            # Convert to window coordinates
            lParam = win32api.MAKELONG(x, y)
            win32api.PostMessage(self.game_hwnd, win32con.WM_LBUTTONDOWN, win32con.MK_LBUTTON, lParam)
            time.sleep(0.05)
            win32api.PostMessage(self.game_hwnd, win32con.WM_LBUTTONUP, 0, lParam)
        except Exception as e:
            print(f"Error clicking at {x}, {y}: {e}")

    # Bot Logic Methods
    def bot_main_loop(self):
        """Main bot execution loop"""
        while self.running:
            if self.paused:
                time.sleep(0.1)
                continue

            try:
                # Check if game window still exists
                if not win32gui.IsWindow(self.game_hwnd):
                    if self.config['auto_features']['auto_login']:
                        self.auto_login()
                    else:
                        self.status_var.set("Game window lost!")
                        break

                # Main bot cycle
                self.monitor_connection()
                self.check_and_repair()
                self.auto_silfrijan()
                self.reply_to_pm()
                self.cast_buffs()
                self.check_dialogs()
                self.find_target()
                self.use_skills()
                self.auto_loot()
                self.check_healing()
                self.advanced_anti_stuck_check()
                self.check_equipment_durability()
                self.auto_answer_quiz()
                self.update_character_stats()

                # Small delay between cycles
                time.sleep(0.1)

            except Exception as e:
                print(f"Error in bot loop: {e}")
                time.sleep(1)

        self.stop_bot()

    def find_target(self):
        """Find and target a monster from the target list"""
        target_delay = self.config['targeting']['target_delay'] / 1000.0
        if time.time() - self.last_target_time < target_delay:
            return

        try:
            # Check if current target is still valid
            if self.config['targeting']['auto_retarget']:
                if self.check_current_target():
                    return  # Current target is still valid

            # Check if we have monsters in our target list
            if not self.config['monster_list']:
                self.fallback_targeting()
                return

            # Priority targeting - target monsters in list order
            if self.config['targeting']['priority_targeting']:
                for monster_name in self.config['monster_list']:
                    if not self.running or self.paused:
                        break

                    success = self.target_monster_by_name(monster_name)
                    if success:
                        self.status_var.set(f"Targeting: {monster_name}")
                        break

            # Target nearest monster using Tab key
            elif self.config['targeting']['target_nearest']:
                self.target_nearest_monster()

            # Fallback to general targeting
            else:
                self.fallback_targeting()

            self.last_target_time = time.time()

        except Exception as e:
            print(f"Error in find_target: {e}")

    def fallback_targeting(self):
        """Fallback targeting method"""
        try:
            if self.config['skills']['use_r_key']:
                self.send_key_to_game('r')
            else:
                # Send 'te' command for general targeting
                self.send_key_to_game('t')
                time.sleep(0.05)
                self.send_key_to_game('e')
                time.sleep(0.05)
                self.send_key_to_game('enter')
        except Exception as e:
            print(f"Error in fallback_targeting: {e}")

    def target_monster_by_name(self, monster_name):
        """Target a specific monster by name using multiple methods"""
        try:
            # Method 1: Use 'te monster_name' command (most reliable)
            success = self.target_with_te_command(monster_name)
            if success:
                return True

            # Method 2: Use tab targeting and check name
            success = self.target_with_tab_cycling(monster_name)
            if success:
                return True

            # Method 3: Use partial name matching
            success = self.target_with_partial_name(monster_name)
            if success:
                return True

            return False

        except Exception as e:
            print(f"Error targeting monster {monster_name}: {e}")
            return False

    def target_with_te_command(self, monster_name):
        """Target using 'te monster_name' command"""
        try:
            # Clear any existing text
            self.send_key_to_game('escape')
            time.sleep(0.05)

            # Send 'te monster_name' command
            self.send_key_to_game('t')
            time.sleep(0.05)
            self.send_key_to_game('e')
            time.sleep(0.05)
            self.send_key_to_game('space')
            time.sleep(0.05)

            # Type monster name
            for char in monster_name:
                if not self.running or self.paused:
                    return False
                self.send_key_to_game(char)
                time.sleep(0.02)

            self.send_key_to_game('enter')
            time.sleep(0.2)

            # Verify target was successful (would need memory reading)
            return self.verify_target(monster_name)

        except Exception as e:
            print(f"Error in target_with_te_command: {e}")
            return False

    def target_with_tab_cycling(self, monster_name):
        """Target by cycling through nearby monsters with Tab"""
        try:
            max_cycles = 10  # Don't cycle forever

            for i in range(max_cycles):
                if not self.running or self.paused:
                    return False

                self.send_key_to_game('tab')
                time.sleep(0.1)

                # Check if current target matches our monster
                current_target = self.get_current_target_name()
                if current_target and monster_name.lower() in current_target.lower():
                    return True

            return False

        except Exception as e:
            print(f"Error in target_with_tab_cycling: {e}")
            return False

    def target_with_partial_name(self, monster_name):
        """Target using partial name matching"""
        try:
            # Try with first word only
            first_word = monster_name.split()[0] if ' ' in monster_name else monster_name

            self.send_key_to_game('escape')
            time.sleep(0.05)
            self.send_key_to_game('t')
            time.sleep(0.05)
            self.send_key_to_game('e')
            time.sleep(0.05)
            self.send_key_to_game('space')
            time.sleep(0.05)

            # Type first word only
            for char in first_word:
                if not self.running or self.paused:
                    return False
                self.send_key_to_game(char)
                time.sleep(0.02)

            self.send_key_to_game('enter')
            time.sleep(0.2)

            return self.verify_target(monster_name)

        except Exception as e:
            print(f"Error in target_with_partial_name: {e}")
            return False

    def verify_target(self, expected_monster):
        """Verify that the target is the expected monster"""
        try:
            # This would need memory reading to get actual target name
            # For now, return True as placeholder
            current_target = self.get_current_target_name()

            if current_target:
                # Check if target name contains expected monster name
                return expected_monster.lower() in current_target.lower()

            return False

        except Exception as e:
            print(f"Error verifying target: {e}")
            return False

    def target_nearest_monster(self):
        """Target the nearest monster using tab key"""
        try:
            # Use tab key to cycle through nearby targets
            self.send_key_to_game('tab')
            time.sleep(0.1)

            # Check if target is in our monster list (would need memory reading)
            # For now, return True
            return True

        except Exception as e:
            print(f"Error in target_nearest_monster: {e}")
            return False

    def check_current_target(self):
        """Check if current target is valid and in monster list"""
        try:
            # This would need memory reading to get current target name
            # For now, return placeholder
            current_target = self.get_current_target_name()

            if current_target and current_target in self.config['monster_list']:
                return True
            return False

        except Exception as e:
            print(f"Error checking current target: {e}")
            return False

    def get_current_target_name(self):
        """Get the name of the currently targeted monster"""
        try:
            # This would need memory reading from target base address
            # Placeholder implementation
            return ""

        except Exception as e:
            print(f"Error getting current target name: {e}")
            return ""

    def use_skills(self):
        """Use combat skills"""
        try:
            for i in range(4):
                if self.paused or not self.running:
                    break

                skill_num = i + 1
                if self.config['skills'][f'skill{skill_num}_enabled']:
                    self.send_key_to_game(str(skill_num))
                    delay = self.config['skills'][f'skill{skill_num}_delay'] / 1000.0
                    time.sleep(delay)

        except Exception as e:
            print(f"Error in use_skills: {e}")

    def auto_loot(self):
        """Automatically loot items"""
        if self.config['skills']['loot_enabled']:
            try:
                self.send_key_to_game('f')
                delay = self.config['skills']['loot_delay'] / 1000.0
                time.sleep(delay)
            except Exception as e:
                print(f"Error in auto_loot: {e}")

    def cast_buffs(self):
        """Cast buffs when needed"""
        current_time = time.time() * 1000  # Convert to milliseconds

        try:
            for i in range(4):
                buff_num = i + 1
                if self.config['buffs'][f'buff{buff_num}_enabled']:
                    duration = self.config['buffs'][f'buff{buff_num}_duration']

                    if current_time - self.last_buff_times[i] >= duration:
                        # Switch to F2 mode
                        self.send_key_to_game('f2')
                        time.sleep(0.1)

                        # Cast buff
                        self.send_key_to_game(str(buff_num))
                        time.sleep(0.1)

                        # Switch back to F1 mode
                        self.send_key_to_game('f1')

                        self.last_buff_times[i] = current_time
                        time.sleep(2)  # Wait between buffs

        except Exception as e:
            print(f"Error in cast_buffs: {e}")

    def check_healing(self):
        """Check and perform healing actions"""
        try:
            # This would need actual memory reading to get HP/TP values
            # For now, we'll use placeholder logic

            # HP Healing (Key 7)
            if self.config['healing']['heal_skill_enabled']:
                # Placeholder: assume we need healing
                self.send_key_to_game('7')
                delay = self.config['healing']['heal_delay'] / 1000.0
                time.sleep(delay)

            # HP Potions (Key 8)
            if self.config['healing']['potion_enabled']:
                # Placeholder: assume we need potions
                self.send_key_to_game('f1')
                time.sleep(0.05)
                self.send_key_to_game('8')
                delay = self.config['healing']['potion_delay'] / 1000.0
                time.sleep(delay)

            # TP Potions (Key 9)
            if self.config['healing']['tp_potion_enabled']:
                # Placeholder: assume we need TP
                self.send_key_to_game('f1')
                time.sleep(0.05)
                self.send_key_to_game('9')
                delay = self.config['healing']['tp_delay'] / 1000.0
                time.sleep(delay)

        except Exception as e:
            print(f"Error in check_healing: {e}")

    def auto_silfrijan(self):
        """Auto-resurrect using Silfrijan"""
        if not self.config['auto_features']['auto_silf']:
            return

        try:
            # This would need memory reading to check if character is dead
            # For now, placeholder logic

            # Click on Silfrijan position
            x = self.config['auto_features']['silf_x']
            y = self.config['auto_features']['silf_y']
            self.click_in_game(x, y)
            time.sleep(0.5)

            # Click OK or confirm
            self.click_in_game(515, 370)
            time.sleep(0.5)

        except Exception as e:
            print(f"Error in auto_silfrijan: {e}")

    def check_and_repair(self):
        """Check equipment durability and repair if needed"""
        if not self.config['auto_features']['auto_repair']:
            return

        try:
            # This would need memory reading to check equipment durability
            # For now, placeholder logic

            # Switch to F2 mode and use repair skill (slot 9)
            self.send_key_to_game('f2')
            time.sleep(0.1)
            self.send_key_to_game('9')
            time.sleep(0.1)
            self.send_key_to_game('f1')

        except Exception as e:
            print(f"Error in check_and_repair: {e}")

    def check_dialogs(self):
        """Check and handle various game dialogs"""
        try:
            # Party invitations
            if self.config['party']['auto_accept']:
                # This would need memory reading to detect party dialogs
                # Placeholder logic for accepting party
                pass
            else:
                # Deny party invitation
                pass

            # Trade requests
            if self.config['trade']['auto_deny']:
                # This would need memory reading to detect trade dialogs
                # Placeholder logic for denying trade
                pass

        except Exception as e:
            print(f"Error in check_dialogs: {e}")

    def reply_to_pm(self):
        """Auto-reply to private messages"""
        if not self.config['pm_reply']['enabled']:
            return

        try:
            # This would need memory reading to detect new PMs
            # Placeholder logic for PM reply

            # Send reply command
            self.send_key_to_game('v')
            time.sleep(0.1)

            # Type reply message (would need proper text input)
            reply_text = self.config['pm_reply']['reply_text']
            # For now, just send enter
            self.send_key_to_game('enter')

        except Exception as e:
            print(f"Error in reply_to_pm: {e}")

    def anti_stuck_movement(self):
        """Perform anti-stuck movement"""
        if not self.config['auto_features']['anti_stuck']:
            return

        try:
            # Spin movement
            self.send_key_to_game('a', self.config['auto_features']['stuck_spin_time'] / 1000.0)

            # Forward movement
            self.send_key_to_game('w', self.config['auto_features']['stuck_move_time'] / 1000.0)

        except Exception as e:
            print(f"Error in anti_stuck_movement: {e}")

    # Auto-login functionality
    def auto_login(self):
        """Perform automatic login sequence"""
        try:
            self.status_var.set("Starting auto-login...")

            # Launch Tantra
            tantra_path = os.path.join(self.config['login']['tantra_dir'], 'Tantra.exe')
            if os.path.exists(tantra_path):
                subprocess.Popen(tantra_path)

                # Wait for game to load
                time.sleep(self.config['login']['delays']['wait_for_tantra'] / 1000.0)

                # Find game window
                if self.find_game_window():
                    self.perform_login_sequence()
                else:
                    self.status_var.set("Could not find game window after launch")
            else:
                self.status_var.set("Tantra.exe not found!")

        except Exception as e:
            print(f"Error in auto_login: {e}")
            self.status_var.set(f"Auto-login error: {e}")

    def perform_login_sequence(self):
        """Perform the actual login sequence"""
        try:
            # Wait for login screen
            time.sleep(self.config['login']['delays']['wait_for_login'] / 1000.0)

            # Click on username field and enter credentials
            self.click_in_game(520, 440)
            time.sleep(0.1)

            # Clear field and enter username
            for _ in range(15):
                self.send_key_to_game('backspace')

            # Type username (simplified - would need proper text input)
            username = self.config['login']['username']
            for char in username:
                self.send_key_to_game(char)
                time.sleep(0.05)

            # Tab to password field
            self.send_key_to_game('tab')
            time.sleep(0.1)

            # Clear and enter password
            for _ in range(15):
                self.send_key_to_game('backspace')

            password = self.config['login']['password']
            for char in password:
                self.send_key_to_game(char)
                time.sleep(0.05)

            # Press enter to login
            self.send_key_to_game('enter')

            # Wait and select server
            time.sleep(self.config['login']['delays']['server_select'] / 1000.0)
            self.select_server()

            # Wait and select character
            time.sleep(self.config['login']['delays']['character_select'] / 1000.0)
            self.select_character()

            # Enter game
            time.sleep(self.config['login']['delays']['enter_game'] / 1000.0)
            self.send_key_to_game('enter')

            self.status_var.set("Auto-login completed")

        except Exception as e:
            print(f"Error in perform_login_sequence: {e}")

    def select_server(self):
        """Select game server"""
        try:
            server_num = self.config['login']['server_selection']
            # Click on server (simplified coordinates)
            server_y = 200 + (server_num - 1) * 20
            self.click_in_game(478, server_y)
            time.sleep(0.1)
            self.send_key_to_game('enter')
        except Exception as e:
            print(f"Error in select_server: {e}")

    def select_character(self):
        """Select game character"""
        try:
            char_num = self.config['login']['character_selection']
            # Click on character (simplified coordinates)
            char_y = 200 + (char_num - 1) * 30
            self.click_in_game(400, char_y)
            time.sleep(0.1)
            self.send_key_to_game('enter')
        except Exception as e:
            print(f"Error in select_character: {e}")

    # Additional bot features
    def trade_spam(self):
        """Spam trade messages (F9 hotkey)"""
        if not self.trade_spam_running:
            self.start_trade_spam()
        else:
            self.stop_trade_spam()

    def start_trade_spam(self):
        """Start trade spam loop"""
        if not self.trade_spam_running:
            self.trade_spam_running = True
            self.status_var.set("Trade Spam Started")
            threading.Thread(target=self.trade_spam_loop, daemon=True).start()

    def stop_trade_spam(self):
        """Stop trade spam loop"""
        self.trade_spam_running = False
        self.status_var.set("Trade Spam Stopped")

    def trade_spam_loop(self):
        """Continuous trade spam loop"""
        while self.trade_spam_running:
            try:
                if self.game_hwnd and self.config['tools']['trade_spam_enabled']:
                    # Send trade message
                    self.send_key_to_game('f2')
                    time.sleep(0.1)
                    self.send_key_to_game('6')
                    time.sleep(0.1)

                    # Type message (simplified)
                    message = self.config['tools']['trade_spam_message']
                    for char in message:
                        if not self.trade_spam_running:
                            break
                        self.send_key_to_game(char)
                        time.sleep(0.02)

                    self.send_key_to_game('enter')

                    delay = self.config['tools']['trade_spam_delay'] / 1000.0
                    time.sleep(delay)

            except Exception as e:
                print(f"Error in trade_spam_loop: {e}")
                break

    # Advanced Features Implementation
    def monitor_connection(self):
        """Monitor game connection and auto-reconnect if needed"""
        if not self.config['tools']['connection_monitor']:
            return

        try:
            # Check if game window exists
            if not win32gui.IsWindow(self.game_hwnd):
                if self.connection_lost_time == 0:
                    self.connection_lost_time = time.time()
                    self.status_var.set("Connection lost - monitoring...")

                # Auto-reconnect after delay
                if self.config['advanced']['auto_reconnect']:
                    elapsed = (time.time() - self.connection_lost_time) * 1000
                    if elapsed >= self.config['advanced']['reconnect_delay']:
                        self.status_var.set("Attempting auto-reconnect...")
                        self.auto_login()
                        self.connection_lost_time = 0
            else:
                self.connection_lost_time = 0

        except Exception as e:
            print(f"Error in monitor_connection: {e}")

    def advanced_anti_stuck_check(self):
        """Advanced anti-stuck detection and handling"""
        if not self.config['advanced']['advanced_anti_stuck']:
            return

        try:
            # This would need memory reading to get actual position
            # For now, use placeholder logic
            current_damage = self.get_current_damage()

            if current_damage == self.last_damage:
                self.stuck_counter += 1
            else:
                self.stuck_counter = 0
                self.last_damage = current_damage

            # If stuck for too long, perform anti-stuck actions
            threshold = self.config['advanced']['stuck_detection_threshold']
            if self.stuck_counter * 100 >= threshold:  # 100ms per check
                self.perform_advanced_anti_stuck()
                self.stuck_counter = 0

        except Exception as e:
            print(f"Error in advanced_anti_stuck_check: {e}")

    def perform_advanced_anti_stuck(self):
        """Perform advanced anti-stuck movements"""
        try:
            self.status_var.set("Performing anti-stuck movement...")

            # Multiple movement patterns
            movements = [
                ('a', 500),  # Turn left
                ('d', 500),  # Turn right
                ('w', 1000), # Move forward
                ('s', 500),  # Move back
                ('space', 100), # Jump
            ]

            for key, duration in movements:
                if not self.running or self.paused:
                    break
                self.send_key_to_game(key, duration / 1000.0)
                time.sleep(0.2)

        except Exception as e:
            print(f"Error in perform_advanced_anti_stuck: {e}")

    def get_current_damage(self):
        """Get current damage value (placeholder)"""
        # This would need actual memory reading
        return 0

    def update_character_stats(self):
        """Update character statistics display"""
        if not self.config['advanced']['status_monitoring']:
            return

        try:
            # This would need actual memory reading
            stats_text = f"""Character Statistics:
HP: 0/0 (0%)
TP: 0/0 (0%)
Status: Unknown
Target: None
Equipment Durability: 100%
Position: (0, 0)
Last Update: {time.strftime('%H:%M:%S')}"""

            self.stats_text.config(state='normal')
            self.stats_text.delete('1.0', tk.END)
            self.stats_text.insert('1.0', stats_text)
            self.stats_text.config(state='disabled')

        except Exception as e:
            print(f"Error updating character stats: {e}")

    def check_equipment_durability(self):
        """Check equipment durability and repair if needed"""
        try:
            # This would need actual memory reading
            durability = 100.0  # Placeholder

            threshold = self.config['advanced']['repair_threshold']
            if durability <= threshold:
                self.status_var.set(f"Equipment durability low: {durability}%")
                if self.config['auto_features']['auto_repair']:
                    self.perform_repair()

        except Exception as e:
            print(f"Error checking equipment durability: {e}")

    def perform_repair(self):
        """Perform equipment repair"""
        try:
            self.status_var.set("Repairing equipment...")
            self.send_key_to_game('f2')
            time.sleep(0.1)
            self.send_key_to_game('9')  # Repair skill
            time.sleep(0.1)
            self.send_key_to_game('f1')
        except Exception as e:
            print(f"Error in perform_repair: {e}")

    def auto_answer_quiz(self):
        """Automatically answer quiz questions"""
        if not self.config['tools']['auto_answer_quiz']:
            return

        try:
            # This would need dialog detection and quiz file reading
            quiz_file = self.config['tools']['quiz_answers_file']
            if os.path.exists(quiz_file):
                # Load quiz answers
                with open(quiz_file, 'r', encoding='utf-8') as f:
                    quiz_data = f.read()

                # Detect quiz dialog and answer
                # This is placeholder logic
                self.status_var.set("Quiz detected - auto-answering...")

        except Exception as e:
            print(f"Error in auto_answer_quiz: {e}")

    # Tool-specific GUI handlers
    def browse_quiz_file(self):
        """Browse for quiz answers file"""
        filename = filedialog.askopenfilename(
            title="Select Quiz Answers File",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.quiz_file_var.set(filename)

    def create_quiz_file(self):
        """Create a new quiz answers file"""
        filename = filedialog.asksaveasfilename(
            title="Create Quiz Answers File",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("# Quiz Answers File\n")
                    f.write("# Format: Question|Answer\n")
                    f.write("# Example:\n")
                    f.write("What is 2+2?|4\n")
                    f.write("What color is the sky?|Blue\n")

                self.quiz_file_var.set(filename)
                messagebox.showinfo("Success", "Quiz answers file created!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create file: {e}")

    def scan_character_base(self):
        """Scan for character base address"""
        try:
            if not self.game_pid:
                messagebox.showerror("Error", "Game process not found!")
                return

            self.status_var.set("Scanning for character base address...")

            # This would need actual memory scanning
            # Placeholder implementation
            messagebox.showinfo("Memory Scanner", "Character base address scanning not yet implemented.\nThis requires game-specific memory patterns.")

        except Exception as e:
            messagebox.showerror("Error", f"Memory scan failed: {e}")

    def scan_target_base(self):
        """Scan for target base address"""
        try:
            if not self.game_pid:
                messagebox.showerror("Error", "Game process not found!")
                return

            self.status_var.set("Scanning for target base address...")

            # This would need actual memory scanning
            # Placeholder implementation
            messagebox.showinfo("Memory Scanner", "Target base address scanning not yet implemented.\nThis requires game-specific memory patterns.")

        except Exception as e:
            messagebox.showerror("Error", f"Memory scan failed: {e}")

    def search_teleport_address(self):
        """Search for teleport target address"""
        try:
            if not self.game_pid:
                messagebox.showerror("Error", "Game process not found!")
                return

            self.status_var.set("Searching for teleport address...")

            # This would need actual memory scanning for teleport patterns
            messagebox.showinfo("Teleport Scanner", "Teleport address scanning not yet implemented.\nThis requires game-specific teleport patterns.")

        except Exception as e:
            messagebox.showerror("Error", f"Teleport scan failed: {e}")

    def hack_portal(self):
        """Perform portal teleport hack"""
        try:
            if not self.config['teleport']['enabled']:
                messagebox.showwarning("Warning", "Teleport hack is not enabled!")
                return

            address = int(self.teleport_address_var.get(), 16)
            if address == 0:
                messagebox.showerror("Error", "Invalid teleport address!")
                return

            self.status_var.set("Performing teleport hack...")

            # This would need actual memory writing
            messagebox.showinfo("Teleport Hack", "Teleport hack not yet implemented.\nThis requires memory writing to teleport addresses.")

        except ValueError:
            messagebox.showerror("Error", "Invalid address format! Use hexadecimal (e.g., 0x12345678)")
        except Exception as e:
            messagebox.showerror("Error", f"Teleport hack failed: {e}")

    def left_clicker(self):
        """Auto left clicker (F11 hotkey)"""
        if not self.left_clicker_running:
            self.start_left_clicker()
        else:
            self.stop_left_clicker()

    def start_left_clicker(self):
        """Start continuous left clicking"""
        if not self.left_clicker_running:
            self.left_clicker_running = True
            self.status_var.set("Auto Clicker Started")
            threading.Thread(target=self.left_clicker_loop, daemon=True).start()

    def stop_left_clicker(self):
        """Stop continuous left clicking"""
        self.left_clicker_running = False
        self.status_var.set("Auto Clicker Stopped")

    def left_clicker_loop(self):
        """Continuous left clicking loop"""
        while self.left_clicker_running:
            try:
                if self.game_hwnd and self.config['tools']['left_clicker_enabled']:
                    x, y = pyautogui.position()
                    self.click_in_game(x, y)

                delay = self.config['tools']['left_clicker_delay'] / 1000.0
                time.sleep(delay)
            except Exception as e:
                print(f"Error in left_clicker_loop: {e}")
                break

    def login_once(self):
        """Perform single login attempt"""
        if not self.config['login']['username'] or not self.config['login']['password']:
            messagebox.showerror("Error", "Please enter username and password!")
            return

        threading.Thread(target=self.auto_login, daemon=True).start()

    def test_connection(self):
        """Test internet connection"""
        try:
            import urllib.request
            urllib.request.urlopen('http://www.google.com', timeout=5)
            messagebox.showinfo("Connection Test", "Internet connection is working!")
        except:
            messagebox.showerror("Connection Test", "No internet connection!")

    # Menu functions
    def show_main_menu(self):
        """Show main menu (hotkey function)"""
        self.root.deiconify()
        self.root.lift()

    def show_login_menu(self):
        """Show login tab (hotkey function)"""
        self.show_main_menu()
        self.notebook.select(4)  # Select login tab

    # Configuration management
    def update_config_from_gui(self):
        """Update configuration from GUI values"""
        try:
            # Update window title
            self.window_title = self.window_title_var.get()

            # Update skills
            for i in range(4):
                skill_num = i + 1
                self.config['skills'][f'skill{skill_num}_enabled'] = self.skill_vars[i].get()
                self.config['skills'][f'skill{skill_num}_delay'] = int(self.skill_delay_vars[i].get())

            self.config['skills']['use_r_key'] = self.use_r_var.get()
            self.config['skills']['loot_enabled'] = self.loot_var.get()
            self.config['skills']['loot_delay'] = int(self.loot_delay_var.get())

            # Update buffs
            for i in range(4):
                buff_num = i + 1
                self.config['buffs'][f'buff{buff_num}_enabled'] = self.buff_vars[i].get()
                self.config['buffs'][f'buff{buff_num}_duration'] = int(self.buff_duration_vars[i].get())

            # Update healing
            self.config['healing']['heal_skill_enabled'] = self.heal_skill_var.get()
            self.config['healing']['heal_percentage'] = int(self.heal_percentage_var.get())
            self.config['healing']['potion_enabled'] = self.potion_var.get()
            self.config['healing']['potion_percentage'] = int(self.potion_percentage_var.get())
            self.config['healing']['tp_potion_enabled'] = self.tp_potion_var.get()
            self.config['healing']['tp_percentage'] = int(self.tp_percentage_var.get())

            # Update auto features
            self.config['auto_features']['auto_login'] = self.auto_login_var.get()
            self.config['auto_features']['auto_repair'] = self.auto_repair_var.get()
            self.config['auto_features']['auto_silf'] = self.auto_silf_var.get()
            self.config['auto_features']['silf_x'] = int(self.silf_x_var.get())
            self.config['auto_features']['silf_y'] = int(self.silf_y_var.get())
            self.config['auto_features']['anti_stuck'] = self.anti_stuck_var.get()
            self.config['auto_features']['stuck_spin_time'] = int(self.stuck_spin_var.get())
            self.config['auto_features']['stuck_move_time'] = int(self.stuck_move_var.get())

            # Update login
            self.config['login']['username'] = self.username_var.get()
            self.config['login']['password'] = self.password_var.get()
            self.config['login']['tantra_dir'] = self.tantra_dir_var.get()
            self.config['login']['server_selection'] = int(self.server_var.get())
            self.config['login']['character_selection'] = int(self.character_var.get())

            for key, var in self.delay_vars.items():
                self.config['login']['delays'][key] = int(var.get())

            # Update party/trade/PM settings
            self.config['party']['auto_accept'] = self.party_accept_var.get()
            self.config['party']['join_reply'] = self.party_join_var.get()
            self.config['party']['deny_reply'] = self.party_deny_var.get()
            self.config['trade']['auto_deny'] = self.trade_deny_var.get()
            self.config['trade']['deny_reply'] = self.trade_reply_var.get()
            self.config['pm_reply']['enabled'] = self.pm_reply_enabled_var.get()
            self.config['pm_reply']['reply_text'] = self.pm_reply_text_var.get()

            # Update monster list
            self.config['monster_list'] = list(self.monster_listbox.get(0, tk.END))

            # Update targeting options
            self.config['targeting']['priority_targeting'] = self.priority_targeting_var.get()
            self.config['targeting']['target_nearest'] = self.target_nearest_var.get()
            self.config['targeting']['auto_retarget'] = self.auto_retarget_var.get()
            self.config['targeting']['target_delay'] = int(self.target_delay_var.get())

            # Update advanced settings
            self.config['tools']['connection_monitor'] = self.connection_monitor_var.get()
            self.config['advanced']['auto_reconnect'] = self.auto_reconnect_var.get()
            self.config['advanced']['reconnect_delay'] = int(self.reconnect_delay_var.get())
            self.config['advanced']['advanced_anti_stuck'] = self.advanced_anti_stuck_var.get()
            self.config['advanced']['stuck_detection_threshold'] = int(self.stuck_threshold_var.get())
            self.config['advanced']['repair_threshold'] = float(self.repair_threshold_var.get())
            self.config['advanced']['status_monitoring'] = self.status_monitoring_var.get()
            self.config['advanced']['memory_scan_enabled'] = self.memory_scan_var.get()

            # Update tools settings
            self.config['tools']['trade_spam_enabled'] = self.trade_spam_enabled_var.get()
            self.config['tools']['trade_spam_message'] = self.trade_spam_message_var.get()
            self.config['tools']['trade_spam_delay'] = int(self.trade_spam_delay_var.get())
            self.config['tools']['left_clicker_enabled'] = self.left_clicker_enabled_var.get()
            self.config['tools']['left_clicker_delay'] = int(self.left_clicker_delay_var.get())
            self.config['tools']['auto_answer_quiz'] = self.auto_answer_quiz_var.get()
            self.config['tools']['quiz_answers_file'] = self.quiz_file_var.get()

            # Update teleport settings
            self.config['teleport']['enabled'] = self.teleport_enabled_var.get()
            try:
                self.config['teleport']['target_address'] = int(self.teleport_address_var.get(), 16)
            except ValueError:
                self.config['teleport']['target_address'] = 0

        except ValueError as e:
            messagebox.showerror("Configuration Error", f"Invalid value in configuration: {e}")
        except Exception as e:
            print(f"Error updating config: {e}")

    def save_config(self):
        """Save configuration to file"""
        try:
            self.update_config_from_gui()

            filename = filedialog.asksaveasfilename(
                title="Save Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w') as f:
                    json.dump(self.config, f, indent=4)
                messagebox.showinfo("Success", "Configuration saved successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def load_config(self):
        """Load configuration from file"""
        try:
            filename = filedialog.askopenfilename(
                title="Load Configuration",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'r') as f:
                    loaded_config = json.load(f)

                # Update config and GUI
                self.config.update(loaded_config)
                self.update_gui_from_config()
                messagebox.showinfo("Success", "Configuration loaded successfully!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load configuration: {e}")

    def update_gui_from_config(self):
        """Update GUI from current configuration"""
        try:
            # Update window title
            self.window_title_var.set(self.window_title)

            # Update skills
            for i in range(4):
                skill_num = i + 1
                self.skill_vars[i].set(self.config['skills'][f'skill{skill_num}_enabled'])
                self.skill_delay_vars[i].set(str(self.config['skills'][f'skill{skill_num}_delay']))

            self.use_r_var.set(self.config['skills']['use_r_key'])
            self.loot_var.set(self.config['skills']['loot_enabled'])
            self.loot_delay_var.set(str(self.config['skills']['loot_delay']))

            # Update buffs
            for i in range(4):
                buff_num = i + 1
                self.buff_vars[i].set(self.config['buffs'][f'buff{buff_num}_enabled'])
                self.buff_duration_vars[i].set(str(self.config['buffs'][f'buff{buff_num}_duration']))

            # Update healing
            self.heal_skill_var.set(self.config['healing']['heal_skill_enabled'])
            self.heal_percentage_var.set(str(self.config['healing']['heal_percentage']))
            self.potion_var.set(self.config['healing']['potion_enabled'])
            self.potion_percentage_var.set(str(self.config['healing']['potion_percentage']))
            self.tp_potion_var.set(self.config['healing']['tp_potion_enabled'])
            self.tp_percentage_var.set(str(self.config['healing']['tp_percentage']))

            # Update auto features
            self.auto_login_var.set(self.config['auto_features']['auto_login'])
            self.auto_repair_var.set(self.config['auto_features']['auto_repair'])
            self.auto_silf_var.set(self.config['auto_features']['auto_silf'])
            self.silf_x_var.set(str(self.config['auto_features']['silf_x']))
            self.silf_y_var.set(str(self.config['auto_features']['silf_y']))
            self.anti_stuck_var.set(self.config['auto_features']['anti_stuck'])
            self.stuck_spin_var.set(str(self.config['auto_features']['stuck_spin_time']))
            self.stuck_move_var.set(str(self.config['auto_features']['stuck_move_time']))

            # Update login
            self.username_var.set(self.config['login']['username'])
            self.password_var.set(self.config['login']['password'])
            self.tantra_dir_var.set(self.config['login']['tantra_dir'])
            self.server_var.set(str(self.config['login']['server_selection']))
            self.character_var.set(str(self.config['login']['character_selection']))

            for key, var in self.delay_vars.items():
                var.set(str(self.config['login']['delays'][key]))

            # Update party/trade/PM settings
            self.party_accept_var.set(self.config['party']['auto_accept'])
            self.party_join_var.set(self.config['party']['join_reply'])
            self.party_deny_var.set(self.config['party']['deny_reply'])
            self.trade_deny_var.set(self.config['trade']['auto_deny'])
            self.trade_reply_var.set(self.config['trade']['deny_reply'])
            self.pm_reply_enabled_var.set(self.config['pm_reply']['enabled'])
            self.pm_reply_text_var.set(self.config['pm_reply']['reply_text'])

            # Update monster list
            self.monster_listbox.delete(0, tk.END)
            for monster in self.config['monster_list']:
                self.monster_listbox.insert(tk.END, monster)

            # Update targeting options
            self.priority_targeting_var.set(self.config['targeting']['priority_targeting'])
            self.target_nearest_var.set(self.config['targeting']['target_nearest'])
            self.auto_retarget_var.set(self.config['targeting']['auto_retarget'])
            self.target_delay_var.set(str(self.config['targeting']['target_delay']))

            # Update advanced settings
            self.connection_monitor_var.set(self.config['tools']['connection_monitor'])
            self.auto_reconnect_var.set(self.config['advanced']['auto_reconnect'])
            self.reconnect_delay_var.set(str(self.config['advanced']['reconnect_delay']))
            self.advanced_anti_stuck_var.set(self.config['advanced']['advanced_anti_stuck'])
            self.stuck_threshold_var.set(str(self.config['advanced']['stuck_detection_threshold']))
            self.repair_threshold_var.set(str(self.config['advanced']['repair_threshold']))
            self.status_monitoring_var.set(self.config['advanced']['status_monitoring'])
            self.memory_scan_var.set(self.config['advanced']['memory_scan_enabled'])

            # Update tools settings
            self.trade_spam_enabled_var.set(self.config['tools']['trade_spam_enabled'])
            self.trade_spam_message_var.set(self.config['tools']['trade_spam_message'])
            self.trade_spam_delay_var.set(str(self.config['tools']['trade_spam_delay']))
            self.left_clicker_enabled_var.set(self.config['tools']['left_clicker_enabled'])
            self.left_clicker_delay_var.set(str(self.config['tools']['left_clicker_delay']))
            self.auto_answer_quiz_var.set(self.config['tools']['auto_answer_quiz'])
            self.quiz_file_var.set(self.config['tools']['quiz_answers_file'])

            # Update teleport settings
            self.teleport_enabled_var.set(self.config['teleport']['enabled'])
            self.teleport_address_var.set(hex(self.config['teleport']['target_address']))

        except Exception as e:
            print(f"Error updating GUI from config: {e}")

    def run(self):
        """Start the application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()

    def on_closing(self):
        """Handle application closing"""
        if self.running:
            self.stop_bot()
        self.root.quit()
        self.root.destroy()


def main():
    """Main entry point"""
    try:
        # Check if required modules are available
        import tkinter.simpledialog

        # Create and run the bot
        bot = TantraBot()
        bot.run()

    except ImportError as e:
        print(f"Missing required module: {e}")
        print("Please install required packages:")
        print("pip install pyautogui keyboard pywin32")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting bot: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

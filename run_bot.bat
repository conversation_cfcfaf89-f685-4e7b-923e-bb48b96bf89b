@echo off
title Tantra Bot System Launcher
echo.
echo ========================================
echo    Tantra Bot System - Python Version
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking dependencies...
echo.

REM Try to install dependencies if needed
python -m pip install -r requirements.txt >nul 2>&1

REM Run the launcher
echo Starting Tantra Bot System...
echo.
python launcher.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo An error occurred. Press any key to exit.
    pause >nul
)

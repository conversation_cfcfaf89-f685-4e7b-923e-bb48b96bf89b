#!/usr/bin/env python3
"""
Tantra Bot Launcher
Simple launcher script with dependency checking and error handling
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        messagebox.showerror(
            "Python Version Error",
            f"Python 3.7 or higher is required.\nCurrent version: {sys.version}"
        )
        return False
    return True

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'tkinter',
        'pyautogui', 
        'keyboard',
        'win32gui',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'pyautogui':
                import pyautogui
            elif package == 'keyboard':
                import keyboard
            elif package == 'win32gui':
                import win32gui
            elif package == 'psutil':
                import psutil
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        missing_str = '\n'.join(missing_packages)
        result = messagebox.askyesno(
            "Missing Dependencies",
            f"The following packages are missing:\n{missing_str}\n\n"
            "Would you like to install them automatically?"
        )
        
        if result:
            try:
                # Install missing packages
                pip_packages = {
                    'tkinter': None,  # Usually comes with Python
                    'pyautogui': 'pyautogui',
                    'keyboard': 'keyboard', 
                    'win32gui': 'pywin32',
                    'psutil': 'psutil'
                }
                
                for package in missing_packages:
                    pip_package = pip_packages.get(package)
                    if pip_package:
                        subprocess.check_call([sys.executable, '-m', 'pip', 'install', pip_package])
                
                messagebox.showinfo("Success", "Dependencies installed successfully!")
                return True
                
            except subprocess.CalledProcessError as e:
                messagebox.showerror(
                    "Installation Error",
                    f"Failed to install dependencies:\n{e}\n\n"
                    "Please install manually using:\n"
                    "pip install -r requirements.txt"
                )
                return False
        else:
            return False
    
    return True

def check_admin_rights():
    """Check if running with administrator privileges"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def launch_bot():
    """Launch the main bot application"""
    try:
        # Import and run the bot
        from tantra_bot import TantraBot
        
        bot = TantraBot()
        bot.run()
        
    except ImportError as e:
        messagebox.showerror(
            "Import Error",
            f"Failed to import bot module:\n{e}\n\n"
            "Make sure tantra_bot.py is in the same directory."
        )
    except Exception as e:
        messagebox.showerror(
            "Runtime Error",
            f"An error occurred while running the bot:\n{e}"
        )

def main():
    """Main launcher function"""
    # Create a simple root window for messageboxes
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    
    try:
        # Check Python version
        if not check_python_version():
            return
        
        # Check dependencies
        if not check_dependencies():
            return
        
        # Check admin rights (recommended for hotkeys)
        if not check_admin_rights():
            result = messagebox.askyesno(
                "Administrator Rights",
                "The bot is not running with administrator privileges.\n"
                "This may cause issues with global hotkeys.\n\n"
                "Continue anyway?"
            )
            if not result:
                return
        
        # Show startup message
        messagebox.showinfo(
            "Tantra Bot System",
            "Starting Tantra Bot System...\n\n"
            "Make sure Tantra is running before starting the bot.\n"
            "Use F8 to start automation, F10 to pause."
        )
        
        # Launch the bot
        root.destroy()  # Destroy the hidden root window
        launch_bot()
        
    except KeyboardInterrupt:
        print("Launcher interrupted by user")
    except Exception as e:
        messagebox.showerror("Launcher Error", f"Unexpected error: {e}")
    finally:
        try:
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    main()

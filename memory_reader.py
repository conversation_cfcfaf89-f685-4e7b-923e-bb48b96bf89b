#!/usr/bin/env python3
"""
Memory Reader Module for Tantra Bot
Handles reading game memory for character stats, dialogs, etc.
"""

import ctypes
from ctypes import wintypes
import struct

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_VM_READ = 0x0010
PROCESS_VM_WRITE = 0x0020
PROCESS_VM_OPERATION = 0x0008

class MemoryReader:
    """Class for reading and writing process memory"""
    
    def __init__(self, process_id):
        self.process_id = process_id
        self.process_handle = None
        self.open_process()
        
    def open_process(self):
        """Open process handle for memory operations"""
        try:
            self.process_handle = ctypes.windll.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, self.process_id
            )
            if not self.process_handle:
                raise Exception(f"Failed to open process {self.process_id}")
        except Exception as e:
            print(f"Error opening process: {e}")
            
    def close_process(self):
        """Close process handle"""
        if self.process_handle:
            ctypes.windll.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            
    def read_memory(self, address, size):
        """Read memory from specified address"""
        if not self.process_handle:
            return None
            
        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t()
            
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )
            
            if result:
                return buffer.raw
            else:
                return None
                
        except Exception as e:
            print(f"Error reading memory at 0x{address:X}: {e}")
            return None
            
    def write_memory(self, address, data):
        """Write data to specified memory address"""
        if not self.process_handle:
            return False
            
        try:
            bytes_written = ctypes.c_size_t()
            
            result = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                data,
                len(data),
                ctypes.byref(bytes_written)
            )
            
            return bool(result)
            
        except Exception as e:
            print(f"Error writing memory at 0x{address:X}: {e}")
            return False
            
    def read_int(self, address):
        """Read a 4-byte integer from memory"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<I', data)[0]
        return 0
        
    def read_float(self, address):
        """Read a 4-byte float from memory"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<f', data)[0]
        return 0.0
        
    def read_string(self, address, max_length=256):
        """Read a null-terminated string from memory"""
        data = self.read_memory(address, max_length)
        if data:
            # Find null terminator
            null_pos = data.find(b'\x00')
            if null_pos != -1:
                return data[:null_pos].decode('utf-8', errors='ignore')
            else:
                return data.decode('utf-8', errors='ignore')
        return ""
        
    def write_int(self, address, value):
        """Write a 4-byte integer to memory"""
        data = struct.pack('<I', value)
        return self.write_memory(address, data)
        
    def write_float(self, address, value):
        """Write a 4-byte float to memory"""
        data = struct.pack('<f', value)
        return self.write_memory(address, data)


class TantraMemoryReader(MemoryReader):
    """Specialized memory reader for Tantra game"""
    
    def __init__(self, process_id):
        super().__init__(process_id)
        
        # Memory addresses - these need to be updated for current game version
        self.addresses = {
            'character_base': 0x00000000,  # Base address for character data
            'target_base': 0x00000000,     # Base address for target data
            'dialog_base': 0x00000000,     # Base address for dialog data
            'chat_base': 0x00000000,       # Base address for chat data
        }
        
        # Offsets from base addresses
        self.offsets = {
            'hp_current': 0x100,
            'hp_max': 0x104,
            'tp_current': 0x108,
            'tp_max': 0x10C,
            'character_name': 0x200,
            'target_name': 0x300,
            'dialog_text': 0x400,
            'chat_message': 0x500,
            'equipment_durability': 0x600,
            'character_status': 0x700,  # Dead/alive status
        }
        
    def get_character_hp(self):
        """Get current character HP"""
        if self.addresses['character_base']:
            address = self.addresses['character_base'] + self.offsets['hp_current']
            return self.read_int(address)
        return 0
        
    def get_character_max_hp(self):
        """Get character maximum HP"""
        if self.addresses['character_base']:
            address = self.addresses['character_base'] + self.offsets['hp_max']
            return self.read_int(address)
        return 0
        
    def get_character_tp(self):
        """Get current character TP (mana)"""
        if self.addresses['character_base']:
            address = self.addresses['character_base'] + self.offsets['tp_current']
            return self.read_int(address)
        return 0
        
    def get_character_max_tp(self):
        """Get character maximum TP"""
        if self.addresses['character_base']:
            address = self.addresses['character_base'] + self.offsets['tp_max']
            return self.read_int(address)
        return 0
        
    def is_character_dead(self):
        """Check if character is dead"""
        if self.addresses['character_base']:
            address = self.addresses['character_base'] + self.offsets['character_status']
            status = self.read_int(address)
            return status == 0  # Assuming 0 means dead
        return False
        
    def get_character_name(self):
        """Get character name"""
        if self.addresses['character_base']:
            address = self.addresses['character_base'] + self.offsets['character_name']
            return self.read_string(address, 32)
        return ""
        
    def get_target_name(self):
        """Get current target name"""
        if self.addresses['target_base']:
            address = self.addresses['target_base'] + self.offsets['target_name']
            return self.read_string(address, 64)
        return ""
        
    def get_dialog_text(self):
        """Get current dialog text"""
        if self.addresses['dialog_base']:
            address = self.addresses['dialog_base'] + self.offsets['dialog_text']
            return self.read_string(address, 256)
        return ""
        
    def get_chat_message(self):
        """Get latest chat message"""
        if self.addresses['chat_base']:
            address = self.addresses['chat_base'] + self.offsets['chat_message']
            return self.read_string(address, 512)
        return ""
        
    def get_equipment_durability(self):
        """Get equipment durability percentage"""
        if self.addresses['character_base']:
            address = self.addresses['character_base'] + self.offsets['equipment_durability']
            return self.read_float(address)
        return 100.0
        
    def update_addresses(self, new_addresses):
        """Update memory addresses for current game version"""
        self.addresses.update(new_addresses)
        
    def scan_for_pattern(self, pattern, start_address=0x400000, end_address=0x7FFFFFFF):
        """Scan memory for a specific byte pattern"""
        try:
            current_address = start_address
            pattern_bytes = bytes.fromhex(pattern.replace(' ', ''))
            
            while current_address < end_address:
                # Read memory in chunks
                chunk_size = 4096
                data = self.read_memory(current_address, chunk_size)
                
                if data:
                    # Search for pattern in chunk
                    pos = data.find(pattern_bytes)
                    if pos != -1:
                        return current_address + pos
                        
                current_address += chunk_size
                
        except Exception as e:
            print(f"Error scanning memory: {e}")
            
        return None
        
    def find_character_base(self):
        """Attempt to find character base address automatically"""
        # This would need game-specific patterns or signatures
        # Example pattern for character data (needs to be updated)
        pattern = "00 00 00 00 FF FF FF FF"  # Placeholder pattern
        
        address = self.scan_for_pattern(pattern)
        if address:
            self.addresses['character_base'] = address
            return True
        return False


# Example usage and testing functions
def test_memory_reader(process_id):
    """Test memory reading functionality"""
    try:
        reader = TantraMemoryReader(process_id)
        
        print(f"Process ID: {process_id}")
        print(f"Process Handle: {reader.process_handle}")
        
        # Test basic memory reading
        if reader.addresses['character_base']:
            hp = reader.get_character_hp()
            max_hp = reader.get_character_max_hp()
            tp = reader.get_character_tp()
            max_tp = reader.get_character_max_tp()
            
            print(f"HP: {hp}/{max_hp}")
            print(f"TP: {tp}/{max_tp}")
            print(f"Character Dead: {reader.is_character_dead()}")
            print(f"Character Name: {reader.get_character_name()}")
            print(f"Target Name: {reader.get_target_name()}")
        else:
            print("Character base address not set")
            
        reader.close_process()
        
    except Exception as e:
        print(f"Error testing memory reader: {e}")


if __name__ == "__main__":
    # Example usage
    print("Tantra Memory Reader Test")
    print("Note: This requires actual game process and correct memory addresses")
    
    # You would need to find the actual process ID of Tantra
    # and update the memory addresses for your game version
    test_process_id = 1234  # Replace with actual process ID
    test_memory_reader(test_process_id)
